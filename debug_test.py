import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

def debug_test_case():
    """Debug the failing test case"""
    # Set the random seed for reproducibility
    np.random.seed(42)
    
    # Create input matrix X
    X = np.arange(16).reshape(4, 4)
    X = np.random.permutation(X.flatten()).reshape(4, 4)
    
    print("X shape:", X.shape)
    print("X:\n", X)
    
    # Create mask
    mask = np.triu(np.ones((4, 4)) * (-np.inf), k=1)
    print("mask shape:", mask.shape)
    print("mask:\n", mask)
    
    # Create weight matrices
    W_q = np.random.randint(0, 4, size=(4, 4))
    W_k = np.random.randint(0, 5, size=(4, 4))
    W_v = np.random.randint(0, 6, size=(4, 4))
    
    print("W_q shape:", W_q.shape)
    print("W_k shape:", W_k.shape)
    print("W_v shape:", W_v.shape)
    
    # Compute Q, K, V
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    
    # Reshape to add batch dimension
    Q = Q.reshape(1, *Q.shape)
    K = K.reshape(1, *K.shape)
    V = V.reshape(1, *V.shape)
    mask = mask.reshape(1, *mask.shape)
    
    print("After reshaping:")
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    print("mask shape:", mask.shape)
    
    # Compute masked attention
    try:
        result = masked_attention(Q, K, V, mask)
        print("Result shape:", result.shape)
        print("Actual result:")
        print(np.round(result[0]).astype(int))
        
        # Expected result
        expected = np.array([
            [52, 63, 48, 71],
            [103, 109, 46, 99],
            [103, 109, 46, 99],
            [103, 109, 46, 99]
        ])
        
        print("\nExpected result:")
        print(expected)
        
        # Check if they match
        print("\nMatch:", np.allclose(np.round(result[0]).astype(int), expected))
    except Exception as e:
        print("Error:", str(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_test_case()