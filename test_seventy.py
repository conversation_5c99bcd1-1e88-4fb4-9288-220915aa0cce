
from seventy import calculate_brightness

def test_calculate_brightness_valid():
    img = [
        [100, 200],
        [50, 150]
    ]
    assert calculate_brightness(img) == 125.0

def test_calculate_brightness_empty():
    img = []
    assert calculate_brightness(img) == -1

def test_calculate_brightness_inconsistent_lengths():
    img = [
        [100, 200],
        [50]
    ]
    assert calculate_brightness(img) == -1

def test_calculate_brightness_invalid_pixel_value():
    img = [
        [100, 300],
        [50, 150]
    ]
    assert calculate_brightness(img) == -1

def test_calculate_brightness_empty_inner_list():
    img = [[]]
    assert calculate_brightness(img) == -1
