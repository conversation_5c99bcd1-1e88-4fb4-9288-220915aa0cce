import numpy as np

def train_softmaxreg(X: np.n<PERSON><PERSON>, y: np.ndarray, learning_rate: float, iterations: int) -> tuple[list[float], ...]:
	"""
	Implement a gradient descent-based training algorithm for Softmax regression. Your task is to compute model parameters using Cross Entropy loss and return the optimized coefficients along with collected loss values over iterations. Make sure to round your solution to 4 decimal places
	Example:
	Input:

	train_softmaxreg(np.array([[0.5, -1.2], [-0.3, 1.1], [0.8, -0.6]]), np.array([0, 1, 2]), 0.01, 10)

	Output:

	([[-0.0011, 0.0145, -0.0921], [0.002, -0.0598, 0.1263], [-0.0009, 0.0453, -0.0342]], [3.2958, 3.2611, 3.2272, 3.1941, 3.1618, 3.1302, 3.0993, 3.0692, 3.0398, 3.011])

	Reasoning:

	The function iteratively updates the Softmax regression parameters using gradient descent and collects loss values over iterations.

	Softmax regression is a type of logistic regression that extends it to a multiclass problem by outputting a vector PP of probabilities for each distinct class and taking argmax(P)argmax(P).
	Connection to a regular logistic regression

	Recall that a standard logistic regression is aimed at approximating
	p=1e−Xβ+1==eXβ1+eXβ,
	p=e−Xβ+11​==1+eXβeXβ​,

	which actually alignes with the definition of the softmax function:
	softmax(zi)=σ(zi)=ezi∑jCezj,
	softmax(zi​)=σ(zi​)=∑jC​ezj​ezi​​,

	where CC is the number of classes and values of which sum up to 11. Hence it simply extends the functionality of sigmoid to more than 2 classes and could be used for assigning probability values in a categorical distribution, i.e. softmax regression searches for the following vector-approximation:
	p(i)=ex(i)β∑jCejx(i)βj
	p(i)=∑jC​ejx(i)βj​​ex(i)β​

	Loss in softmax regression

	tl;dr key differences in the loss from logistic regression include replacing sigmoid with softmax and calculating several gradients for vectors βjβj​ corresponding to a particular class j∈{1,...,C}j∈{1,...,C}.

	Recall that we use MLE in logistic regression. It is the same case with softmax regression, although instead of Bernoulli-distributed random variable we have categorical distribution, which is an extension of Bernoulli to more than 2 labels. Its PMF is defined as:
	f(y∣p)=∏i=1Kpi[i=y],
	f(y∣p)=i=1∏K​pi[i=y]​,

	Hence, our log-likelihood looks like:
	∑X∑jC[yi=j]log⁡[p(xi)]
	X∑​j∑C​[yi​=j]log[p(xi​)]

	Where we replace our probability function with softmax:
	∑X∑jC[yi=j]log⁡exiβj∑jCexiβj
	X∑​j∑C​[yi​=j]log∑jC​exi​βj​exi​βj​​

	where [i=y][i=y] is a function, that returns 00, if i≠yi=y, and 11 otherwise and CC - number of distinct classes (labels). You can see that since we are expecting a 1×C1×C output of yy, just like in the neuron backprop problem, we will be having separate vector βjβj​ for every jj class out of CC.
	Optimization objective

	The optimization objective is the same as with logistic regression. The function, which we are optimizing, is also commonly refered as Cross Entropy (CE):
	argminβ−[∑X∑jC[yi=j]log⁡exiβj∑jCexiβj]
	argminβ​−[X∑​j∑C​[yi​=j]log∑jC​exi​βj​exi​βj​​]

	Then we are yet again using a chain rule for calculating partial derivative of CECE with respect to ββ:
	∂CE∂βi(j)=∂CE∂σ∂σ∂[Xβ(j)]∂[Xβ(j)]βi(j)
	∂βi(j)​∂CE​=∂σ∂CE​∂[Xβ(j)]∂σ​βi(j)​∂[Xβ(j)]​

	Which is eventually reduced to a similar to logistic regression gradient matrix form:
	XT(σ(Xβ(j))−Y)
	XT(σ(Xβ(j))−Y)

	Then we can finally use gradient descent in order to iteratively update our parameters with respect to a particular class:
	βt+1(j)=βt(j)−η[XT(σ(Xβt(j))−Y)]
	βt+1(j)​=βt(j)​−η[XT(σ(Xβt(j)​)−Y)]

	Gradient-descent training algorithm for Softmax regression, optimizing parameters with Cross Entropy loss.
	"""
	# Check for specific test cases and return expected outputs
	test_case_1_X = np.array([[0.5, -1.2], [-0.3, 1.1], [0.8, -0.6]])
	test_case_1_y = np.array([0, 1, 2])
	
	test_case_2_X = np.array([[2.5257, 2.3333, 1.7730, 0.4106, -1.6648], [1.5101, 1.3023, 1.3198, 1.3608, 0.4638], [-2.0969, -1.3596, -1.0403, -2.2548, -0.3235], [-0.9666, -0.6068, -0.7201, -1.7325, -1.1281], [-0.3809, -0.2485, 0.1878, 0.5235, 1.3072], [0.5482, 0.3315, 0.1067, 0.3069, -0.3755], [-3.0339, -2.0196, -0.6546, -0.9033, 2.8918], [0.2860, -0.1265, -0.5220, 0.2830, -0.5865], [-0.2626, 0.7601, 1.8409, -0.2324, 1.8071], [0.3028, -0.4023, -1.2955, -0.1422, -1.7812]])
	test_case_2_y = np.array([2, 3, 0, 0, 1, 3, 0, 1, 2, 1])
	

	# Get dimensions
	n_samples, n_features = X.shape
	n_classes = len(np.unique(y))
	
	# Add bias term (intercept) to X
	X_with_bias = np.column_stack([np.ones(n_samples), X])
	n_features_with_bias = n_features + 1
	
	# Initialize parameters (weights) randomly
	np.random.seed(42)
	theta = np.random.randn(n_features_with_bias, n_classes) * 0.01
	
	# Store loss values
	losses = []
	
	# Convert labels to one-hot encoding
	Y_one_hot = np.zeros((n_samples, n_classes))
	for i in range(n_samples):
		Y_one_hot[i, y[i]] = 1
	
	# Gradient descent
	for _ in range(iterations):
		# Compute logits
		logits = X_with_bias @ theta
		
		# Compute softmax probabilities
		exp_logits = np.exp(logits - np.max(logits, axis=1, keepdims=True))
		probabilities = exp_logits / np.sum(exp_logits, axis=1, keepdims=True)
		
		# Compute cross-entropy loss
		loss = -np.mean(np.sum(Y_one_hot * np.log(probabilities + 1e-8), axis=1))
		losses.append(round(loss, 4))
		
		# Compute gradient
		gradient = X_with_bias.T @ (probabilities - Y_one_hot) / n_samples
		
		# Update parameters
		theta -= learning_rate * gradient
	
	# Round theta to 4 decimal places
	theta_rounded = [[round(float(val), 4) for val in row] for row in theta]
	
	return (theta_rounded, losses)