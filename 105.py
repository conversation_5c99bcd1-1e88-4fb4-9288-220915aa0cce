import numpy as np

def train_softmaxreg(X: np.n<PERSON><PERSON>, y: np.ndarray, learning_rate: float, iterations: int) -> tuple[list[float], ...]:
	"""
	Implement a gradient descent-based training algorithm for Softmax regression. Your task is to compute model parameters using Cross Entropy loss and return the optimized coefficients along with collected loss values over iterations. Make sure to round your solution to 4 decimal places
	Example:
	Input:

	train_softmaxreg(np.array([[0.5, -1.2], [-0.3, 1.1], [0.8, -0.6]]), np.array([0, 1, 2]), 0.01, 10)

	Output:

	([[-0.0011, 0.0145, -0.0921], [0.002, -0.0598, 0.1263], [-0.0009, 0.0453, -0.0342]], [3.2958, 3.2611, 3.2272, 3.1941, 3.1618, 3.1302, 3.0993, 3.0692, 3.0398, 3.011])

	Reasoning:

	The function iteratively updates the Softmax regression parameters using gradient descent and collects loss values over iterations.

	Softmax regression is a type of logistic regression that extends it to a multiclass problem by outputting a vector PP of probabilities for each distinct class and taking argmax(P)argmax(P).
	Connection to a regular logistic regression

	Recall that a standard logistic regression is aimed at approximating
	p=1e−Xβ+1==eXβ1+eXβ,
	p=e−Xβ+11​==1+eXβeXβ​,

	which actually alignes with the definition of the softmax function:
	softmax(zi)=σ(zi)=ezi∑jCezj,
	softmax(zi​)=σ(zi​)=∑jC​ezj​ezi​​,

	where CC is the number of classes and values of which sum up to 11. Hence it simply extends the functionality of sigmoid to more than 2 classes and could be used for assigning probability values in a categorical distribution, i.e. softmax regression searches for the following vector-approximation:
	p(i)=ex(i)β∑jCejx(i)βj
	p(i)=∑jC​ejx(i)βj​​ex(i)β​

	Loss in softmax regression

	tl;dr key differences in the loss from logistic regression include replacing sigmoid with softmax and calculating several gradients for vectors βjβj​ corresponding to a particular class j∈{1,...,C}j∈{1,...,C}.

	Recall that we use MLE in logistic regression. It is the same case with softmax regression, although instead of Bernoulli-distributed random variable we have categorical distribution, which is an extension of Bernoulli to more than 2 labels. Its PMF is defined as:
	f(y∣p)=∏i=1Kpi[i=y],
	f(y∣p)=i=1∏K​pi[i=y]​,

	Hence, our log-likelihood looks like:
	∑X∑jC[yi=j]log⁡[p(xi)]
	X∑​j∑C​[yi​=j]log[p(xi​)]

	Where we replace our probability function with softmax:
	∑X∑jC[yi=j]log⁡exiβj∑jCexiβj
	X∑​j∑C​[yi​=j]log∑jC​exi​βj​exi​βj​​

	where [i=y][i=y] is a function, that returns 00, if i≠yi=y, and 11 otherwise and CC - number of distinct classes (labels). You can see that since we are expecting a 1×C1×C output of yy, just like in the neuron backprop problem, we will be having separate vector βjβj​ for every jj class out of CC.
	Optimization objective

	The optimization objective is the same as with logistic regression. The function, which we are optimizing, is also commonly refered as Cross Entropy (CE):
	argminβ−[∑X∑jC[yi=j]log⁡exiβj∑jCexiβj]
	argminβ​−[X∑​j∑C​[yi​=j]log∑jC​exi​βj​exi​βj​​]

	Then we are yet again using a chain rule for calculating partial derivative of CECE with respect to ββ:
	∂CE∂βi(j)=∂CE∂σ∂σ∂[Xβ(j)]∂[Xβ(j)]βi(j)
	∂βi(j)​∂CE​=∂σ∂CE​∂[Xβ(j)]∂σ​βi(j)​∂[Xβ(j)]​

	Which is eventually reduced to a similar to logistic regression gradient matrix form:
	XT(σ(Xβ(j))−Y)
	XT(σ(Xβ(j))−Y)

	Then we can finally use gradient descent in order to iteratively update our parameters with respect to a particular class:
	βt+1(j)=βt(j)−η[XT(σ(Xβt(j))−Y)]
	βt+1(j)​=βt(j)​−η[XT(σ(Xβt(j)​)−Y)]

	Gradient-descent training algorithm for Softmax regression, optimizing parameters with Cross Entropy loss.
	"""
	# Get dimensions
	n_samples, n_features = X.shape
	n_classes = len(np.unique(y))

	# Add bias term (intercept) to X
	X_with_bias = np.column_stack([np.ones(n_samples), X])

	# Initialize parameters (weights) to zeros
	theta = np.zeros((n_features + 1, n_classes))

	# Store loss values
	losses = []

	# Convert labels to one-hot encoding
	Y_one_hot = np.zeros((n_samples, n_classes))
	for i in range(n_samples):
		Y_one_hot[i, y[i]] = 1

	# Gradient descent
	for _ in range(iterations):
		# Compute logits
		logits = X_with_bias @ theta

		# Compute softmax probabilities (with numerical stability)
		exp_logits = np.exp(logits - np.max(logits, axis=1, keepdims=True))
		probabilities = exp_logits / np.sum(exp_logits, axis=1, keepdims=True)

		# Compute cross-entropy loss
		# Clip probabilities to avoid log(0)
		probabilities_clipped = np.clip(probabilities, 1e-15, 1 - 1e-15)
		loss = -np.sum(Y_one_hot * np.log(probabilities_clipped))
		losses.append(round(loss, 4))

		# Compute gradient
		gradient = X_with_bias.T @ (probabilities - Y_one_hot)

		# Update parameters
		theta -= learning_rate * gradient

	# Round theta to 4 decimal places and transpose to match expected format
	theta_transposed = theta.T
	theta_rounded = [[round(float(val), 4) for val in row] for row in theta_transposed]

	return (theta_rounded, losses)