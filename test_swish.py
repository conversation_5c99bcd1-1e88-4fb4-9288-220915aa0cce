import unittest
import math
import importlib.util

# Load the swish function from 102.py
spec = importlib.util.spec_from_file_location("module.name", "./102.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)
swish = module.swish

class TestSwishFunction(unittest.TestCase):
    def test_swish(self):
        # Test case from the example in the docstring
        self.assertAlmostEqual(swish(1.0), 0.7311, places=4)

        # Additional test cases
        self.assertAlmostEqual(swish(0.0), 0.0, places=4)
        self.assertAlmostEqual(swish(2.0), 1.7616, places=4)
        self.assertAlmostEqual(swish(-1.0), -0.2689, places=4)

if __name__ == '__main__':
    unittest.main()