import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

# Test Case
np.random.seed(42)
X = np.arange(16).reshape(4, 4)
X = np.random.permutation(X.flatten()).reshape(4, 4)
mask = np.triu(np.ones((4, 4)) * (-np.inf), k=1)
W_q = np.random.randint(0, 4, size=(4, 4))
W_k = np.random.randint(0, 5, size=(4, 4))
W_v = np.random.randint(0, 6, size=(4, 4))
Q, K, V = compute_qkv(X, W_q, W_k, W_v)

# Add batch dimension
Q = Q.reshape(1, *Q.shape)
K = K.reshape(1, *K.shape)
V = V.reshape(1, *V.shape)
mask = mask.reshape(1, *mask.shape)

result = masked_attention(Q, K, V, mask)
print(np.round(result[0]).astype(int))