import numpy as np
import importlib.util
import numpy as np

# Load the train_softmaxreg function from 105.py
spec = importlib.util.spec_from_file_location("module.name", "./105.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)
train_softmaxreg = module.train_softmaxreg

X = np.array([[2.5257, 2.3333, 1.7730, 0.4106, -1.6648],
              [1.5101, 1.3023, 1.3198, 1.3608, 0.4638],
              [-2.0969, -1.3596, -1.0403, -2.2548, -0.3235],
              [-0.9666, -0.6068, -0.7201, -1.7325, -1.1281],
              [-0.3809, -0.2485, 0.1878, 0.5235, 1.3072],
              [0.5482, 0.3315, 0.1067, 0.3069, -0.3755],
              [-3.0339, -2.0196, -0.6546, -0.9033, 2.8918],
              [0.2860, -0.1265, -0.5220, 0.2830, -0.5865],
              [-0.2626, 0.7601, 1.8409, -0.2324, 1.8071],
              [0.3028, -0.4023, -1.2955, -0.1422, -1.7812]])

y = np.array([2, 3, 0, 0, 1, 3, 0, 1, 2, 1])

theta, losses = train_softmaxreg(X, y, 0.03, 10)

print("Theta:")
for row in theta:
    print(row)

print("\nLosses:")
print(losses)