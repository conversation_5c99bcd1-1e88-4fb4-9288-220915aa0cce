def phi_corr(x: list[int], y: list[int]) -> float:
	"""
    Implement a function to calculate the Phi coefficient, a measure of the correlation between two binary variables. The function should take two lists of integers (0s and 1s) as input and return the Phi coefficient rounded to 4 decimal places.
    Example:
    Input:

    phi_corr([1, 1, 0, 0], [0, 0, 1, 1])

    Output:

    -1.0

    Reasoning:

    The Phi coefficient measures the correlation between two binary variables. In this example, the variables have a perfect negative correlation, resulting in a Phi coefficient of -1.0.

	Calculate the Phi coefficient between two binary variables.

	Args:
	x (list[int]): A list of binary values (0 or 1).
	y (list[int]): A list of binary values (0 or 1).

	Returns:
	float: The Phi coefficient rounded to 4 decimal places.
	"""
	if len(x) != len(y):
		raise ValueError("Lists must have the same length")
	
	# Count occurrences for each combination
	n11 = 0  # x=1, y=1
	n10 = 0  # x=1, y=0
	n01 = 0  # x=0, y=1
	n00 = 0  # x=0, y=0
	
	for xi, yi in zip(x, y):
		if xi == 1 and yi == 1:
			n11 += 1
		elif xi == 1 and yi == 0:
			n10 += 1
		elif xi == 0 and yi == 1:
			n01 += 1
		elif xi == 0 and yi == 0:
			n00 += 1
	
	# Calculate Phi coefficient
	numerator = n11 * n00 - n10 * n01
	denominator = (n11 + n10) * (n00 + n01) * (n11 + n01) * (n00 + n10)
	
	if denominator == 0:
		return 0.0
	
	phi = numerator / (denominator ** 0.5)
	return round(phi, 4)