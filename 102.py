def swish(x: float) -> float:
	"""
    Implement the Swish activation function, a self-gated activation function that has shown superior performance in deep neural networks compared to ReLU. Your task is to compute the Swish value for a given input.
    Example:
    Input:

    swish(1)

    Output:

    0.7311

    Reasoning:

    For x = 1, the Swish activation is calculated as Swish(x)=x×σ(x)Swish(x)=x×σ(x), where σ(x)=11+e−xσ(x)=1+e−x1​. Substituting the value, Swish(1)=1×11+e−1=0.7311Swish(1)=1×1+e−11​=0.7311.

	Implements the Swish activation function.

	Args:
		x: Input value

	Returns:
		The Swish activation value
	"""
	import math

	return x * (1 / (1 + math.exp(-x)))