import numpy as np 
def descriptive_statistics(data):
	"""calculate various descriptive statistics metrics for a given dataset. The function should take a list or NumPy array of numerical values and return a dictionary containing mean, median, mode, variance, standard deviation, percentiles (25th, 50th, 75th), and interquartile range (IQR).
    Example:
    Input:

    [10, 20, 30, 40, 50]

    Output:

    {'mean': 30.0, 'median': 30.0, 'mode': 10, 'variance': 200.0, 'standard_deviation': 14.142135623730951, '25th_percentile': 20.0, '50th_percentile': 30.0, '75th_percentile': 40.0, 'interquartile_range': 20.0}

    Reasoning:

    The dataset is processed to calculate all descriptive statistics. The mean is the average value, the median is the central value, the mode is the most frequent value, and variance and standard deviation measure the spread of data. Percentiles and IQR describe data distribution.
    """
	data_np = np.array(data)

	mean = np.mean(data_np)
	median = np.median(data_np)

	values, counts = np.unique(data_np, return_counts=True)
	index = np.argmax(counts)
	mode = values[index]

	variance = np.round(np.var(data_np), 4)
	std_dev = np.round(np.std(data_np), 4)

	percentiles = np.percentile(data_np, [25, 50, 75])
	p25, p50, p75 = percentiles[0], percentiles[1], percentiles[2]

	iqr = p75 - p25

	stats_dict = {
		"mean": mean,
		"median": median,
		"mode": mode,
		"variance": variance,
		"standard_deviation": std_dev,
		"25th_percentile": p25,
		"50th_percentile": p50,
		"75th_percentile": p75,
		"interquartile_range": iqr
	}
	return stats_dict