import unittest
import math
import importlib.util

# Load the selu function from 103.py
spec = importlib.util.spec_from_file_location("module.name", "./103.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)
selu = module.selu

class TestSeluFunction(unittest.TestCase):
    def test_selu(self):
        # Test case from the example in the docstring
        self.assertAlmostEqual(selu(-1.0), -1.1113, places=4)

        # Additional test cases
        self.assertAlmostEqual(selu(0.0), 0.0, places=4)
        self.assertAlmostEqual(selu(1.0), 1.0507, places=4)
        self.assertAlmostEqual(selu(-2.0), -1.5202, places=4)

if __name__ == '__main__':
    unittest.main()