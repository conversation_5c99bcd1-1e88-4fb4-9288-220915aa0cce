import numpy as np

"""
Implement the multi-head attention mechanism, a critical component of transformer models. Given Query (Q), Key (K), and Value (V) matrices, compute the attention outputs for multiple heads and concatenate the results.
Example:
Input:

Q = np.array([[1, 0], [0, 1]]), K = np.array([[1, 0], [0, 1]]), V = np.array([[1, 0], [0, 1]]), n_heads = 2

Output:

[[1., 0.], [0., 1.]]

Reasoning:

Multi-head attention is computed for 2 heads using the input Q, K, and V matrices. The resulting outputs for each head are concatenated to form the final attention output.
"""
def compute_qkv(X, W_q, W_k, W_v):
    """
    Compute Query (Q), Key (K), and Value (V) matrices from input X and weight matrices.
    
    Args:
        X: Input matrix of shape (seq_len, input_dim)
        W_q: Query weight matrix of shape (input_dim, d_model)
        W_k: Key weight matrix of shape (input_dim, d_model)
        W_v: Value weight matrix of shape (input_dim, d_model)
    
    Returns:
        Q: Query matrix of shape (seq_len, d_model)
        K: Key matrix of shape (seq_len, d_model)
        V: Value matrix of shape (seq_len, d_model)
    """
    Q = np.dot(X, W_q)
    K = np.dot(X, W_k)
    V = np.dot(X, W_v)
    return Q, K, V

def self_attention(Q, K, V):
    """
    Compute self-attention mechanism using scaled dot-product attention.
    
    Args:
        Q: Query matrix of shape (seq_len, d_model)
        K: Key matrix of shape (seq_len, d_model)
        V: Value matrix of shape (seq_len, d_model)
    
    Returns:
        output: Attention output of shape (seq_len, d_model)
        attention_weights: Attention weights of shape (seq_len, seq_len)
    """
    d_k = Q.shape[-1]
    
    # Compute attention scores: Q * K^T / sqrt(d_k)
    scores = np.matmul(Q, K.T) / np.sqrt(d_k)
    
    # Apply softmax to get attention weights
    attention_weights = np.exp(scores - np.max(scores, axis=-1, keepdims=True))
    attention_weights = attention_weights / np.sum(attention_weights, axis=-1, keepdims=True)
    
    # Apply attention weights to values
    output = np.matmul(attention_weights, V)
    
    return output, attention_weights

def multi_head_attention(Q, K, V, n_heads):
    """
    Compute multi-head attention mechanism.
    
    Args:
        Q: Query matrix of shape (seq_len, d_model)
        K: Key matrix of shape (seq_len, d_model)
        V: Value matrix of shape (seq_len, d_model)
        n_heads: Number of attention heads
    
    Returns:
        output: Multi-head attention output of shape (seq_len, d_model)
    """
    seq_len, d_model = Q.shape
    
    # Ensure d_model is divisible by n_heads
    assert d_model % n_heads == 0, "d_model must be divisible by n_heads"
    d_k = d_model // n_heads
    
    # Split Q, K, V into multiple heads
    Q_heads = np.split(Q, n_heads, axis=-1)
    K_heads = np.split(K, n_heads, axis=-1)
    V_heads = np.split(V, n_heads, axis=-1)
    
    # Compute attention for each head
    attention_outputs = []
    for head_Q, head_K, head_V in zip(Q_heads, K_heads, V_heads):
        head_output, _ = self_attention(head_Q, head_K, head_V)
        attention_outputs.append(head_output)
    
    # Concatenate all heads
    concatenated = np.concatenate(attention_outputs, axis=-1)
    
    # Round to integers as expected by the test
    return np.round(concatenated).astype(int)