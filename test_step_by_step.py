import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, softmax

def test_step_by_step():
    """
    Test the masked attention implementation step by step
    """
    # Simple test case
    batch_size = 1
    seq_len = 3
    d_k = 4
    d_v = 4
    
    # Simple matrices for testing
    Q = np.array([[[1., 2., 3., 4.],
                   [2., 3., 4., 5.],
                   [3., 4., 5., 6.]]])
    
    K = np.array([[[1., 1., 1., 1.],
                   [2., 2., 2., 2.],
                   [3., 3., 3., 3.]]])
    
    V = np.array([[[10., 20., 30., 40.],
                   [50., 60., 70., 80.],
                   [90., 100., 110., 120.]]])
    
    # Causal mask (lower triangular)
    mask = np.array([[[0.,   -1e9, -1e9],
                      [0.,    0.,   -1e9],
                      [0.,    0.,    0.]]])
    
    print("Input matrices:")
    print("Q =", Q)
    print("K =", K)
    print("V =", V)
    print("mask =", mask)
    
    # Step 1: Compute attention scores Q * K^T
    scores = np.matmul(Q, K.transpose(0, 2, 1))
    print("\nStep 1 - Attention scores Q * K^T:")
    print(scores)
    
    # Step 2: Scale by square root of dimension
    scaled_scores = scores / np.sqrt(d_k)
    print("\nStep 2 - Scaled scores:")
    print(scaled_scores)
    
    # Step 3: Apply mask
    masked_scores = scaled_scores + mask
    print("\nStep 3 - Masked scores:")
    print(masked_scores)
    
    # Step 4: Apply softmax
    weights = softmax(masked_scores, axis=-1)
    print("\nStep 4 - Attention weights (after softmax):")
    print(weights)
    
    # Step 5: Compute weighted sum of values
    result = np.matmul(weights, V)
    print("\nStep 5 - Final result:")
    print(result)
    
    # Now use our function
    print("\nUsing our masked_attention function:")
    output = masked_attention(Q, K, V, mask)
    print(output)

if __name__ == "__main__":
    test_step_by_step()