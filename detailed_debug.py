import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

def detailed_debug():
    """Detailed debug of the failing test case"""
    # Set the random seed for reproducibility
    np.random.seed(42)
    
    # Create input matrix X
    X = np.arange(48).reshape(6, 8)
    X = np.random.permutation(X.flatten()).reshape(6, 8)
    
    print("X shape:", X.shape)
    print("X:\n", X)
    
    # Create mask
    mask = np.triu(np.ones((6, 6)) * (-np.inf), k=1)
    print("mask shape:", mask.shape)
    print("mask:\n", mask)
    
    # Create weight matrices
    W_q = np.random.randint(0, 4, size=(8, 8))
    W_k = np.random.randint(0, 5, size=(8, 8))
    W_v = np.random.randint(0, 6, size=(8, 8))
    
    print("W_q shape:", W_q.shape)
    print("W_k shape:", W_k.shape)
    print("W_v shape:", W_v.shape)
    
    # Compute Q, K, V
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    print("Q:\n", Q)
    print("K:\n", K)
    print("V:\n", V)
    
    # Check if we need to add batch dimension
    # For masked_attention, we need:
    # Q: (batch_size, seq_len, d_k)
    # K: (batch_size, seq_len, d_k)
    # V: (batch_size, seq_len, d_v)
    # mask: (seq_len, seq_len) or (batch_size, seq_len, seq_len)
    
    # Currently Q, K, V have shape (seq_len, d_model) which needs to be reshaped
    Q = Q.reshape(1, *Q.shape)  # Add batch dimension
    K = K.reshape(1, *K.shape)  # Add batch dimension
    V = V.reshape(1, *V.shape)  # Add batch dimension
    # mask already has shape (seq_len, seq_len), we can keep it as is or add batch dimension
    
    print("\nAfter reshaping:")
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    print("mask shape:", mask.shape)
    
    # Try with mask having batch dimension
    mask_with_batch = mask.reshape(1, *mask.shape)
    print("mask_with_batch shape:", mask_with_batch.shape)
    
    # Compute masked attention
    try:
        result = masked_attention(Q, K, V, mask_with_batch)
        print("Result shape:", result.shape)
        print("Actual result:")
        print(np.round(result[0]).astype(int))
        
        # Expected result
        expected = np.array([
            [547, 490, 399, 495, 485, 439, 645, 393],
            [547, 490, 399, 495, 485, 439, 645, 393],
            [471, 472, 429, 538, 377, 450, 531, 362],
            [471, 472, 429, 538, 377, 450, 531, 362],
            [471, 472, 429, 538, 377, 450, 531, 362],
            [471, 472, 429, 538, 377, 450, 531, 362]
        ])
        
        print("\nExpected result:")
        print(expected)
        
        # Check if they match
        print("\nMatch:", np.allclose(np.round(result[0]).astype(int), expected))
    except Exception as e:
        print("Error with batch dimension mask:", str(e))
        import traceback
        traceback.print_exc()
    
    # Try with mask without batch dimension
    try:
        result = masked_attention(Q, K, V, mask)
        print("Result shape:", result.shape)
        print("Actual result with non-batch mask:")
        print(np.round(result[0]).astype(int))
    except Exception as e:
        print("Error with non-batch dimension mask:", str(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    detailed_debug()