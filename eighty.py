import math

def normal_pdf(x, mean, std_dev):
	"""
	Calculate the probability density function (PDF) of the normal distribution.
	:param x: The value at which the PDF is evaluated.
	:param mean: The mean (μ) of the distribution.
	:param std_dev: The standard deviation (σ) of the distribution.
	
     calculate the probability density function (PDF) of the normal distribution for a given value, mean, and standard deviation. The function should use the mathematical formula of the normal distribution to return the PDF value rounded to 5 decimal places.
    Example:
    Input:

    x = 16, mean = 15, std_dev = 2.04

    Output:

    0.17342

    Reasoning:

    The function computes the PDF using x = 16, mean = 15, and std_dev = 2.04.
    """
	if std_dev <= 0:
		raise ValueError("Standard deviation must be positive.")
	
	variance = std_dev ** 2
	exponent = -((x - mean) ** 2) / (2 * variance)
	coefficient = 1 / (math.sqrt(2 * math.pi * variance))
	
	val = coefficient * math.exp(exponent)
	
	return round(val,5)