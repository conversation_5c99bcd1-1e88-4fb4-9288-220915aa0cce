import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention

def create_test_case():
    """
    Create a test case that reproduces the exact example output.
    We'll reverse engineer the inputs to match the expected output.
    """
    
    # Expected output from the example
    expected = np.array([
        [547., 490., 399., 495., 485., 439., 645., 393.],
        [547., 490., 399., 495., 485., 439., 645., 393.],
        [471., 472., 429., 538., 377., 450., 531., 362.],
        [471., 472., 429., 538., 377., 450., 531., 362.],
        [471., 472., 429., 538., 377., 450., 531., 362.],
        [471., 472., 429., 538., 377., 450., 531., 362.]
    ])
    
    # Try to construct inputs that would produce this output
    # This is a simplified approach - in practice, we'd need to solve for the inputs
    batch_size = 1
    seq_len = 6
    d_model = 8
    
    # Create simple Q, K, V matrices
    Q = np.array([[
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [2., 2., 2., 2., 2., 2., 2., 2.],
        [2., 2., 2., 2., 2., 2., 2., 2.],
        [2., 2., 2., 2., 2., 2., 2., 2.],
        [2., 2., 2., 2., 2., 2., 2., 2.]
    ]])
    
    K = np.array([[
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [1., 1., 1., 1., 1., 1., 1., 1.],
        [1., 1., 1., 1., 1., 1., 1., 1.]
    ]])
    
    V = np.array([[
        [547., 490., 399., 495., 485., 439., 645., 393.],
        [547., 490., 399., 495., 485., 439., 645., 393.],
        [471., 472., 429., 538., 377., 450., 531., 362.],
        [471., 472., 429., 538., 377., 450., 531., 362.],
        [471., 472., 429., 538., 377., 450., 531., 362.],
        [471., 472., 429., 538., 377., 450., 531., 362.]
    ]])
    
    # Create causal mask (lower triangular)
    mask = np.triu(np.ones((seq_len, seq_len)) * -1e9, k=1)
    mask = np.expand_dims(mask, axis=0)  # Add batch dimension
    
    print("Q:")
    print(Q[0])
    print("\nK:")
    print(K[0])
    print("\nV:")
    print(V[0])
    print("\nMask:")
    print(mask[0])
    
    # Compute masked attention
    result = masked_attention(Q, K, V, mask)
    
    print("\nResult:")
    print(np.round(result[0], 0))
    print("\nExpected:")
    print(expected)
    print("\nDifference:")
    print(np.round(result[0] - expected, 2))

if __name__ == "__main__":
    create_test_case()