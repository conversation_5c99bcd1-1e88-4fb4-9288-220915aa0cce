import numpy as np

def predict_logistic(X: np.n<PERSON><PERSON>, weights: np.ndarray, bias: float) -> np.ndarray:
	"""
    Implement the prediction function for binary classification using Logistic Regression. Your task is to compute class probabilities using the sigmoid function and return binary predictions based on a threshold of 0.5.
    Example:
    Input:

    predict_logistic(np.array([[1, 1], [2, 2], [-1, -1], [-2, -2]]), np.array([1, 1]), 0)

    Output:

    [1 1 0 0]

    Reasoning:

    Each sample's linear combination is computed using z=Xw+bz=Xw+b. The sigmoid function is applied, and the output is thresholded at 0.5, resulting in binary predictions.

	Implements binary classification prediction using Logistic Regression.

	Args:
		X: Input feature matrix (shape: N x D)
		weights: Model weights (shape: D)
		bias: Model bias

	Returns:
		Binary predictions (0 or 1)
	"""
	# Compute linear combination
	z = np.dot(X, weights) + bias

	z = np.clip(z, -500, 500)

	# Apply sigmoid function
	probabilities = 1 / (1 + np.exp(-z))

	# Apply threshold of 0.5
	predictions = (probabilities >= 0.5).astype(int)

	return predictions