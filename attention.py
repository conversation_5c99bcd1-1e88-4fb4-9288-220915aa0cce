import numpy as np

def softmax(x, axis=-1):
    """
    Compute softmax values for array x along specified axis.
    
    Args:
        x: Input array
        axis: Axis along which to compute softmax
        
    Returns:
        Softmax values with same shape as input
    """
    # Subtract max for numerical stability
    x_max = np.max(x, axis=axis, keepdims=True)
    exp_x = np.exp(x - x_max)
    return exp_x / np.sum(exp_x, axis=axis, keepdims=True)

def compute_qkv(X: np.ndarray, W_q: np.ndarray, W_k: np.ndarray, W_v: np.ndarray):
    """
    Compute Query (Q), Key (K), and Value (V) matrices.
    
    Args:
        X: Input matrix of shape (seq_len, d_model)
        W_q: Weight matrix for query of shape (d_model, d_k)
        W_k: Weight matrix for key of shape (d_model, d_k)
        W_v: Weight matrix for value of shape (d_model, d_v)
        
    Returns:
        Tuple of (Q, K, V) matrices
    """
    return np.dot(X, W_q), np.dot(X, W_k), np.dot(X, W_v)

def masked_attention(Q: np.ndarray, K: np.ndarray, V: np.ndarray, mask: np.ndarray) -> np.ndarray:
    """
    Compute masked self-attention.
    
    Args:
        Q: Query matrix of shape (batch_size, seq_len, d_k)
        K: Key matrix of shape (batch_size, seq_len, d_k)
        V: Value matrix of shape (batch_size, seq_len, d_v)
        mask: Mask matrix of shape (seq_len, seq_len) or (batch_size, seq_len, seq_len)
              Should contain -inf or large negative values where attention is to be masked.
        
    Returns:
        Attention output of shape (batch_size, seq_len, d_v)
    """
    # Ensure arrays are numpy arrays
    Q = np.asarray(Q)
    K = np.asarray(K)
    V = np.asarray(V)
    mask = np.asarray(mask)
    
    # Compute attention scores: Q * K^T
    scores = np.matmul(Q, K.transpose(0, 2, 1))  # Shape: (batch_size, seq_len, seq_len)
    
    # Scale by square root of dimension
    d_k = Q.shape[-1]
    scores = scores / np.sqrt(d_k)
    
    # Apply mask
    # The mask should have -inf or large negative values where we want to mask
    scores = scores + mask  # Broadcasting handles shape differences
    
    # Apply softmax to get attention weights
    weights = softmax(scores, axis=-1)  # Shape: (batch_size, seq_len, seq_len)
    
    # Compute weighted sum of values
    attention_output = np.matmul(weights, V)  # Shape: (batch_size, seq_len, d_v)
    
    return attention_output