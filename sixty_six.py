
def orthogonal_projection(v, L):
	"""
	Compute the orthogonal projection of vector v onto line L.

	:param v: The vector to be projected
	:param L: The line vector defining the direction of projection
	:return: List representing the projection of v onto L
    
    implement a function that calculates the orthogonal projection of a vector v onto another vector L. This projection results in the vector on L that is closest to v.

    Write a function orthogonal_projection(v, L) that takes in two lists, v (the vector to be projected) and L (the line vector), and returns the orthogonal projection of v onto L. The function should output a list representing the projection vector rounded to three decimal places.
    Example:
    Input:

    v = [3, 4]
    L = [1, 0]
    print(orthogonal_projection(v, L))

    Output:

    [3.0, 0.0]

    Reasoning:

    The orthogonal projection of vector [3, 4] onto the line defined by [1, 0] results in the projection vector [3, 0], which lies on the line [1, 0].	
        
    """
	# Calculate the dot product of v and L
	dot_product_vL = sum(a * b for a, b in zip(v, L))
	
	# Calculate the dot product of L with itself (squared magnitude of L)
	dot_product_LL = sum(a * b for a, b in zip(L, L))
	
	# Avoid division by zero if L is the zero vector
	if dot_product_LL == 0:
		return [0.0] * len(v)
		
	# Calculate the scalar for projection
	scalar = dot_product_vL / dot_product_LL
	
	# Calculate the projection vector
	projection = [scalar * component for component in L]
	
	# Round the result to three decimal places
	rounded_projection = [round(x, 3) for x in projection]
	
	return rounded_projection
