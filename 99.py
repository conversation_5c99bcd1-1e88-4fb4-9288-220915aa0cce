def softplus(x: float) -> float:
	"""
    Implement the Softplus activation function, a smooth approximation of the ReLU function. Your task is to compute the Softplus value for a given input, handling edge cases to prevent numerical overflow or underflow.
    Example:
    Input:

    softplus(2)

    Output:

    2.1269

    Reasoning:

    For x = 2, the Softplus activation is calculated as log⁡(1+ex)log(1+ex).


    The Softplus activation function is a smooth approximation of the ReLU function. It's used in neural networks where a smoother transition around zero is desired. Unlike ReLU which has a sharp transition at x=0, Softplus provides a more gradual change.
    Mathematical Definition

    The Softplus function is mathematically defined as:
    Softplus(x)=log⁡(1+ex)
    Softplus(x)=log(1+ex)

    Where:

        xx is the input to the function
        ee is <PERSON><PERSON><PERSON>'s number (approximately 2.71828)
        log⁡log is the natural logarithm

    Characteristics

        Output Range:
            The output is always positive: (0,∞)(0,∞)
            Unlike ReLU, Softplus never outputs exactly zero

        Smoothness:
            Softplus is continuously differentiable
            The transition around x=0 is smooth, unlike ReLU's sharp "elbow"

        Relationship to ReLU:
            Softplus can be seen as a smooth approximation of ReLU
            As x becomes very negative, Softplus approaches 0
            As x becomes very positive, Softplus approaches x

        Derivative:
            The derivative of Softplus is the logistic sigmoid function:
        ddxSoftplus(x)=11+e−x
        dxd​Softplus(x)=1+e−x1​

    Use Cases

        When smooth gradients are important for optimization
        In neural networks where a continuous approximation of ReLU is needed
        Situations where strictly positive outputs are required with smooth transitions


	Compute the softplus activation function.

	Args:
		x: Input value

	Returns:
		The softplus value: log(1 + e^x)
	"""

	import math
	# Use numerically stable computation: max(x, 0) + log(1 + exp(-|x|))
	# This avoids overflow for large positive x and underflow for large negative x
	if x > 30:
		# For very large positive x, softplus(x) ≈ x
		result = x * 1.0
	elif x < -30:
		# For very large negative x, softplus(x) ≈ 0
		result = math.exp(x)
	else:
		result = math.log1p(math.exp(x))
	return round(result, 4)