def calculate_brightness(img):
	"""
    implement a function calculate_brightness(img) that calculates the average brightness of a grayscale image. The image is represented as a 2D matrix, where each element represents a pixel value between 0 (black) and 255 (white).
    Your Task:

    Implement the function calculate_brightness(img) to:

        Return the average brightness of the image rounded to two decimal places.
        Handle edge cases:
            If the image matrix is empty.
            If the rows in the matrix have inconsistent lengths.
            If any pixel values are outside the valid range (0-255).

    For any of these edge cases, the function should return -1.
    Example:
    Input:

    img = [
        [100, 200],
        [50, 150]
    ]
    print(calculate_brightness(img))

    Output:

    125.0

    Reasoning:

    The average brightness is calculated as (100 + 200 + 50 + 150) / 4 = 125.0
	"""
	if not img or not all(isinstance(row, list) for row in img):
		return -1

	if not img[0]:
		return -1

	if not all(len(row) == len(img[0]) for row in img):
		return -1

	total_brightness = 0
	num_pixels = 0

	for row in img:
		for pixel in row:
			if not isinstance(pixel, (int, float)) or not (0 <= pixel <= 255):
				return -1
			total_brightness += pixel
			num_pixels += 1

	if num_pixels == 0:
		return -1

	average_brightness = total_brightness / num_pixels
	return round(average_brightness, 2)