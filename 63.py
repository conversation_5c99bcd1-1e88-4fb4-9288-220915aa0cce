import numpy as np

def conjugate_gradient(A, b, n, x0=None, tol=1e-8):
	"""
	Solve the system Ax = b using the Conjugate Gradient method.

	:param A: Symmetric positive-definite matrix
	:param b: Right-hand side vector
	:param n: Maximum number of iterations
	:param x0: Initial guess for solution (default is zero vector)
	:param tol: Convergence tolerance
	:return: Solution vector x

    implement the Conjugate Gradient (CG) method, an efficient iterative algorithm for solving large, sparse, symmetric, positive-definite linear systems. Given a matrix A and a vector b, the algorithm will solve for x in the system ( Ax = b ).

    Write a function conjugate_gradient(A, b, n, x0=None, tol=1e-8) that performs the Conjugate Gradient method as follows:

        A: A symmetric, positive-definite matrix representing the linear system.
        b: The vector on the right side of the equation.
        n: Maximum number of iterations.
        x0: Initial guess for the solution vector.
        tol: Tolerance for stopping criteria.

    The function should return the solution vector x.
    Example:
    Input:

    A = np.array([[4, 1], [1, 3]])
    b = np.array([1, 2])
    n = 5

    print(conjugate_gradient(A, b, n))

    Output:

    [0.09090909, 0.63636364]

    Reasoning:

    The Conjugate Gradient method is applied to the linear system Ax = b with the given matrix A and vector b. The algorithm iteratively refines the solution to converge to the exact solution.

	"""
	if x0 is None:
		x = np.zeros_like(b, dtype=np.float64)
	else:
		x = x0.astype(np.float64)

	r = b - A @ x
	p = r.copy()
	rs_old = r @ r

	for _ in range(n):
		Ap = A @ p
		alpha = rs_old / (p @ Ap)
		x += alpha * p
		r -= alpha * Ap
		rs_new = r @ r
		if np.sqrt(rs_new) < tol:
			break
		p = r + (rs_new / rs_old) * p
		rs_old = rs_new

	return x

if __name__ == '__main__':
    A = np.array([[4, 1], [1, 3]])
    b = np.array([1, 2])
    n = 5
    print(conjugate_gradient(A, b, n))