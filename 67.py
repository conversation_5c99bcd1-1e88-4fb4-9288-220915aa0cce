def compressed_col_sparse_matrix(dense_matrix):
    """
    Convert a dense matrix into its Compressed Column Sparse (CSC) representation.

    :param dense_matrix: List of lists representing the dense matrix
    :return: Tuple of (values, row indices, column pointer)
    
    implement a function that converts a dense matrix into its Compressed Column Sparse (CSC) representation. The CSC format stores only non-zero elements of the matrix and is efficient for matrices with a high number of zero elements.

    Write a function compressed_col_sparse_matrix(dense_matrix) that takes in a two-dimensional list dense_matrix and returns a tuple of three lists:

        values: List of non-zero elements, stored in column-major order.
        row indices: List of row indices corresponding to each value in the values array.
        column pointer: List that indicates the starting index of each column in the values array.

    Example:
    Input:

    dense_matrix = [
        [0, 0, 3, 0],
        [1, 0, 0, 4],
        [0, 2, 0, 0]
    ]

    vals, row_idx, col_ptr = compressed_col_sparse_matrix(dense_matrix)

    Output:

    [1, 2, 3, 4] [1, 2, 0, 1] [0, 1, 2, 3, 4]

    Reasoning:

    The dense matrix is converted to CSC format with the values array containing non-zero elements, row indices array storing the corresponding row index, and column pointer array indicating the start of each column in the values array.    
    """
    if not dense_matrix or not dense_matrix[0]:
        return [], [], [0]

    num_rows = len(dense_matrix)
    num_cols = len(dense_matrix[0])
    
    values = []
    row_indices = []
    col_ptr = [0]
    
    # Iterate through columns
    for col in range(num_cols):
        # Iterate through rows
        for row in range(num_rows):
            if dense_matrix[row][col] != 0:
                values.append(dense_matrix[row][col])
                row_indices.append(row)
        col_ptr.append(len(values))
        
    return values, row_indices, col_ptr
