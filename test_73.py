import numpy as np
from seventy_three import dice_score

def test_dice_score():
    y_true = np.array([1, 1, 0, 1, 0, 1])
    y_pred = np.array([1, 1, 0, 0, 0, 1])
    assert dice_score(y_true, y_pred) == 0.857

def test_dice_score_perfect():
    y_true = np.array([1, 1, 1, 1])
    y_pred = np.array([1, 1, 1, 1])
    assert dice_score(y_true, y_pred) == 1.0

def test_dice_score_no_overlap():
    y_true = np.array([1, 1, 1, 1])
    y_pred = np.array([0, 0, 0, 0])
    assert dice_score(y_true, y_pred) == 0.0

def test_dice_score_empty():
    y_true = np.array([])
    y_pred = np.array([])
    assert dice_score(y_true, y_pred) == 1.0
