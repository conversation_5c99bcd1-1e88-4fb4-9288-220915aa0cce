def prelu(x: float, alpha: float = 0.25) -> float:
	"""
    Implement the PReLU (Parametric ReLU) activation function, a variant of the ReLU activation function that introduces a learnable parameter for negative inputs. Your task is to compute the PReLU activation value for a given input.
    Example:
    Input:

    prelu(-2.0, alpha=0.25)

    Output:

    -0.5

    Reasoning:

    For x = -2.0 and alpha = 0.25, the PReLU activation is calculated as PReLU(x)=αx=0.25×−2.0=−0.5PReLU(x)=αx=0.25×−2.0=−0.5.
	Implements the PReLU (Parametric ReLU) activation function.

	Args:
		x: Input value
		alpha: Slope parameter for negative values (default: 0.25)

	Returns:
		float: PReLU activation value
	"""
	return x if x >= 0 else alpha * x
	