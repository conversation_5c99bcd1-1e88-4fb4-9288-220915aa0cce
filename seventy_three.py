import numpy as np

def dice_score(y_true, y_pred):
	"""implement a function dice_score(y_true, y_pred) that calculates the Dice Score, also known as the SÃ¸rensen-Dice coefficient or F1-score, for binary classification. The Dice Score is used to measure the similarity between two sets and is particularly useful in tasks like image segmentation and binary classification.
    Your Task:

    Implement the function dice_score(y_true, y_pred) to:

        Calculate the Dice Score between the arrays y_true and y_pred.
        Return the Dice Score as a float value rounded to 3 decimal places.
        Handle edge cases appropriately, such as when there are no true or predicted positives.

    The Dice Score is defined as:
    Dice Score=2×(Number of elements in the intersection of ytrue and ypred)Number of elements in ytrue+Number of elements in ypred
    Dice Score=Number of elements in ytrue​+Number of elements in ypred​2×(Number of elements in the intersection of ytrue​ and ypred​)​

    Where:

        ytrueytrue​ and ypredypred​ are binary arrays of the same length, representing true and predicted labels.
        The result ranges from 0 (no overlap) to 1 (perfect overlap).

    Example:
    Input:

    y_true = np.array([1, 1, 0, 1, 0, 1])
    y_pred = np.array([1, 1, 0, 0, 0, 1])
    print(dice_score(y_true, y_pred))

    Output:

    0.857

    Reasoning:

    The Dice Score is calculated as (2 * 3) / (2 * 3 + 0 + 1) = 0.857, indicating an 85.7% overlap between the true and predicted labels.
    """
	intersection = np.sum(y_true * y_pred)
	sum_true = np.sum(y_true)
	sum_pred = np.sum(y_pred)

	denominator = sum_true + sum_pred

	if denominator == 0:
		return 0.0

	res = (2. * intersection) / denominator
	return round(res, 3)