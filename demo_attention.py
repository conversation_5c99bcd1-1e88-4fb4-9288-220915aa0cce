import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

def demo():
    """
    Demonstrate the masked_attention function with example inputs
    """
    print("=== Masked Self-Attention Implementation Demo ===\n")
    
    # Example 1: Simple case
    print("Example 1: Simple masked attention")
    batch_size, seq_len, d_k, d_v = 1, 4, 3, 3
    
    # Create sample Q, K, V matrices
    Q = np.array([[[1., 2., 3.],
                   [2., 3., 4.],
                   [3., 4., 5.],
                   [4., 5., 6.]]])
    
    K = np.array([[[1., 1., 1.],
                   [2., 2., 2.],
                   [3., 3., 3.],
                   [4., 4., 4.]]])
    
    V = np.array([[[10., 20., 30.],
                   [40., 50., 60.],
                   [70., 80., 90.],
                   [100., 110., 120.]]])
    
    # Create causal mask (lower triangular)
    mask = np.triu(np.ones((seq_len, seq_len)) * -1e9, k=1)
    mask = np.expand_dims(mask, axis=0)  # Add batch dimension
    
    print(f"Q shape: {Q.shape}")
    print(f"K shape: {K.shape}")
    print(f"V shape: {V.shape}")
    print(f"Mask shape: {mask.shape}")
    
    # Compute masked attention
    result = masked_attention(Q, K, V, mask)
    
    print(f"Output shape: {result.shape}")
    print("Output:")
    print(result[0])
    print()
    
    # Example 2: Using compute_qkv
    print("Example 2: Using compute_qkv function")
    # Create input sequence and weight matrices
    X = np.array([[1., 2., 3.],
                  [2., 3., 4.],
                  [3., 4., 5.],
                  [4., 5., 6.]])
    
    W_q = np.array([[1., 0., 0.],
                    [0., 1., 0.],
                    [0., 0., 1.]])
    
    W_k = np.array([[1., 0., 0.],
                    [0., 1., 0.],
                    [0., 0., 1.]])
    
    W_v = np.array([[2., 0., 0.],
                    [0., 2., 0.],
                    [0., 0., 2.]])
    
    # Compute Q, K, V
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    Q = np.expand_dims(Q, axis=0)  # Add batch dimension
    K = np.expand_dims(K, axis=0)  # Add batch dimension
    V = np.expand_dims(V, axis=0)  # Add batch dimension
    
    # Apply the same mask
    mask = np.triu(np.ones((X.shape[0], X.shape[0])) * -1e9, k=1)
    mask = np.expand_dims(mask, axis=0)  # Add batch dimension
    
    print(f"X shape: {X.shape}")
    print(f"W_q shape: {W_q.shape}")
    print(f"Q shape: {Q.shape}")
    print(f"K shape: {K.shape}")
    print(f"V shape: {V.shape}")
    
    # Compute masked attention
    result = masked_attention(Q, K, V, mask)
    
    print(f"Output shape: {result.shape}")
    print("Output:")
    print(result[0])

if __name__ == "__main__":
    demo()