
import numpy as np

def matrix_image(A):
    """
    implement a function matrix_image(A) that calculates the column space of a given matrix A. The column space, also known as the image or span, consists of all linear combinations of the columns of A. To find this, you'll use concepts from linear algebra, focusing on identifying independent columns that span the matrix's image. Your task: Implement the function matrix_image(A) to return the basis vectors that span the column space of A. These vectors should be extracted from the original matrix and correspond to the independent columns.
    Example:
    Input:

    matrix = np.array([
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9]
    ])
    print(matrix_image(matrix))

    Output:

    # [[1, 2],
    #  [4, 5],
    #  [7, 8]]

    Reasoning:

    The column space of the matrix is spanned by the independent columns [1, 2], [4, 5], and [7, 8]. These columns form the basis vectors that represent the image of the matrix.    
    """
    if A.size == 0:
        return np.array([[]])

    basis_columns = []
    for i in range(A.shape[1]):
        current_col = A[:, i]
        
        if basis_columns:
            basis_matrix = np.column_stack(basis_columns)
            test_matrix = np.column_stack((basis_matrix, current_col))
        else:
            test_matrix = np.column_stack([current_col])
            
        if np.linalg.matrix_rank(test_matrix) > len(basis_columns):
            basis_columns.append(current_col)

    if not basis_columns:
        return np.empty((A.shape[0], 0))

    return np.column_stack(basis_columns)
