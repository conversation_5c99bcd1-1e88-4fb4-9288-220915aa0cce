import math

def binomial_probability(n, k, p):
	"""
    Calculate the probability of achieving exactly k successes in n independent Bernoulli trials,
    each with probability p of success, using the Binomial distribution formula.
    
    calculate the probability of achieving exactly k successes in n independent Bernoulli trials, each with probability p of success, using the Binomial distribution formula.
    Example:
    Input:

    n = 6, k = 2, p = 0.5

    Output:

    0.23438

    Reasoning:

    The function calculates the Binomial probability, the intermediate steps include calculating the binomial coefficient, raising p and (1-p) to the appropriate powers, and multiplying the results.
    """
	# Check if the probability p is valid (between 0 and 1).
	if not (0 <= p <= 1):
		# Raise an error if p is not in the valid range.
		raise ValueError("Probability p must be between 0 and 1.")
	# Check if the number of successes k is valid.
	if k < 0 or k > n:
		# If k is out of the valid range [0, n], the probability is 0.
		return 0.0

	# Calculate the binomial coefficient (n choose k).
	binomial_coefficient = math.comb(n, k)
	# Calculate the binomial probability using the formula.
	probability = binomial_coefficient * (p ** k) * ((1 - p) ** (n - k))
	# Return the calculated probability, rounded to 5 decimal places.
	return round(probability, 5)