import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

def debug_second_case():
    """Debug the second test case"""
    # Set the random seed for reproducibility
    np.random.seed(42)
    
    # Create input matrix X
    X = np.arange(16).reshape(4, 4)
    X = np.random.permutation(X.flatten()).reshape(4, 4)
    print("X shape:", X.shape)
    print("X:\n", X)
    
    # Create mask
    mask = np.triu(np.ones((4, 4)) * (-np.inf), k=1)
    print("mask shape:", mask.shape)
    print("mask:\n", mask)
    
    # Create weight matrices
    W_q = np.random.randint(0, 4, size=(4, 4))
    W_k = np.random.randint(0, 5, size=(4, 4))
    W_v = np.random.randint(0, 6, size=(4, 4))
    print("W_q shape:", W_q.shape)
    print("W_k shape:", W_k.shape)
    print("W_v shape:", W_v.shape)
    
    # Compute Q, K, V
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    
    # Add batch dimension
    Q = Q.reshape(1, *Q.shape)
    K = K.reshape(1, *K.shape)
    V = V.reshape(1, *V.shape)
    print("After reshaping:")
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    
    # Try the computation step by step
    try:
        # Step 1: Compute attention scores Q * K^T
        print("\nStep 1 - Computing Q * K^T")
        scores = np.matmul(Q, K.transpose(0, 2, 1))
        print("scores shape:", scores.shape)
        print("scores:\n", scores)
        
        # Step 2: Scale by square root of dimension
        d_k = Q.shape[-1]
        print("\nStep 2 - Scaling by sqrt(d_k), d_k =", d_k)
        scaled_scores = scores / np.sqrt(d_k)
        print("scaled_scores shape:", scaled_scores.shape)
        print("scaled_scores:\n", scaled_scores)
        
        # Step 3: Apply mask
        print("\nStep 3 - Applying mask")
        print("mask shape:", mask.shape)
        masked_scores = scaled_scores + mask
        print("masked_scores shape:", masked_scores.shape)
        print("masked_scores:\n", masked_scores)
        
        # Step 4: Apply softmax
        from attention import softmax
        print("\nStep 4 - Applying softmax")
        weights = softmax(masked_scores, axis=-1)
        print("weights shape:", weights.shape)
        print("weights:\n", weights)
        
        # Step 5: Compute weighted sum of values
        print("\nStep 5 - Computing weighted sum")
        attention_output = np.matmul(weights, V)
        print("attention_output shape:", attention_output.shape)
        print("attention_output:\n", attention_output)
        
        print("\nFinal result (rounded):")
        print(np.round(attention_output[0]).astype(int))
        
        # Expected result
        expected = np.array([
            [ 52,  63,  48,  71],
            [103, 109,  46,  99],
            [103, 109,  46,  99],
            [103, 109,  46,  99]
        ])
        print("\nExpected result:")
        print(expected)
        
        # Check if they match
        print("\nMatch:", np.allclose(np.round(attention_output[0]).astype(int), expected))
        
    except Exception as e:
        print("Error:", str(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_second_case()