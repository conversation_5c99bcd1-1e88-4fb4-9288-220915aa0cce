import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv, softmax

def test_softmax():
    """Test the softmax function"""
    print("Testing softmax function...")
    x = np.array([[1, 2, 3], [4, 5, 6]])
    result = softmax(x, axis=-1)
    expected = np.array([[0.09003057, 0.24472847, 0.66524096], 
                         [0.09003057, 0.24472847, 0.66524096]])
    assert np.allclose(result, expected), "Softmax test failed"
    print("Softmax test passed!")

def test_compute_qkv():
    """Test the compute_qkv function"""
    print("Testing compute_qkv function...")
    X = np.array([[1, 2], [3, 4]])
    W_q = np.array([[1, 0], [0, 1]])
    W_k = np.array([[2, 0], [0, 2]])
    W_v = np.array([[3, 0], [0, 3]])
    
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    
    expected_Q = np.array([[1, 2], [3, 4]])
    expected_K = np.array([[2, 4], [6, 8]])
    expected_V = np.array([[3, 6], [9, 12]])
    
    assert np.allclose(Q, expected_Q), "Q computation failed"
    assert np.allclose(K, expected_K), "K computation failed"
    assert np.allclose(V, expected_V), "V computation failed"
    print("compute_qkv test passed!")

def test_masked_attention_case1():
    """Test masked attention with the first test case"""
    print("Testing masked attention - case 1...")
    np.random.seed(42)
    X = np.arange(48).reshape(6,8)
    X = np.random.permutation(X.flatten()).reshape(6, 8)
    mask = np.triu(np.ones((6, 6))*(-np.inf), k=1)
    W_q = np.random.randint(0,4,size=(8,8))
    W_k = np.random.randint(0,5,size=(8,8))
    W_v = np.random.randint(0,6,size=(8,8))
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    
    # Add batch dimension
    Q = Q.reshape(1, *Q.shape)
    K = K.reshape(1, *K.shape)
    V = V.reshape(1, *V.shape)
    
    result = masked_attention(Q, K, V, mask)
    expected = np.array([
        [547, 490, 399, 495, 485, 439, 645, 393],
        [547, 490, 399, 495, 485, 439, 645, 393],
        [471, 472, 429, 538, 377, 450, 531, 362],
        [471, 472, 429, 538, 377, 450, 531, 362],
        [471, 472, 429, 538, 377, 450, 531, 362],
        [471, 472, 429, 538, 377, 450, 531, 362]
    ])
    
    assert np.allclose(np.round(result[0]).astype(int), expected), "Masked attention case 1 failed"
    print("Masked attention case 1 passed!")

def test_masked_attention_case2():
    """Test masked attention with the second test case"""
    print("Testing masked attention - case 2...")
    np.random.seed(42)
    X = np.arange(16).reshape(4,4)
    X = np.random.permutation(X.flatten()).reshape(4, 4)
    mask = np.triu(np.ones((4, 4))*(-np.inf), k=1)
    W_q = np.random.randint(0,4,size=(4,4))
    W_k = np.random.randint(0,5,size=(4,4))
    W_v = np.random.randint(0,6,size=(4,4))
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    
    # Add batch dimension
    Q = Q.reshape(1, *Q.shape)
    K = K.reshape(1, *K.shape)
    V = V.reshape(1, *V.shape)
    
    result = masked_attention(Q, K, V, mask)
    expected = np.array([
        [ 52,  63,  48,  71],
        [103, 109,  46,  99],
        [103, 109,  46,  99],
        [103, 109,  46,  99]
    ])
    
    assert np.allclose(np.round(result[0]).astype(int), expected), "Masked attention case 2 failed"
    print("Masked attention case 2 passed!")

def test_masked_attention_simple():
    """Test masked attention with a simple case"""
    print("Testing masked attention - simple case...")
    # Simple test case
    Q = np.array([[[1., 2., 3., 4.],
                   [2., 3., 4., 5.],
                   [3., 4., 5., 6.]]])
    
    K = np.array([[[1., 1., 1., 1.],
                   [2., 2., 2., 2.],
                   [3., 3., 3., 3.]]])
    
    V = np.array([[[10., 20., 30., 40.],
                   [50., 60., 70., 80.],
                   [90., 100., 110., 120.]]])
    
    # Causal mask (lower triangular)
    mask = np.array([[[0.,   -np.inf, -np.inf],
                      [0.,    0.,   -np.inf],
                      [0.,    0.,    0.]]])
    
    result = masked_attention(Q, K, V, mask)
    # Expected result based on manual calculation
    expected = np.array([[[ 10.,  20.,  30.,  40.],
                          [ 49.96355795,  59.96355795,  69.96355795,  79.96355795],
                          [ 89.995063,  99.995063,  109.995063,  119.995063]]])
    
    assert np.allclose(result, expected, rtol=1e-5), "Simple masked attention test failed"
    print("Simple masked attention test passed!")

if __name__ == "__main__":
    print("Running comprehensive tests for attention.py implementation...")
    print("="*50)
    
    test_softmax()
    test_compute_qkv()
    test_masked_attention_simple()
    test_masked_attention_case1()
    test_masked_attention_case2()
    
    print("="*50)
    print("All tests passed! Implementation is correct.")