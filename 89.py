import numpy as np

def softmax(values):
	# Implement the softmax function
	exps = np.exp(values - np.max(values))
	return exps / np.sum(exps)

def pattern_weaver(n, crystal_values, dimension):
	"""Deep in the Crystal Cave, the enigmatic <PERSON><PERSON> creates stunning sequences by uncovering the intricate relationships between crystals. Each crystal is marked by a unique numeric value, and the <PERSON> emphasizes that the true power of any crystal depends on how it interacts with all others. You have discovered N crystals, each with a specific value, and your task is to reveal their enhanced patterns by analyzing these relationships using self-attention. Given a sequence of crystals and their values, your task is to implement a simplified self-attention mechanism. For each crystal, calculate its relationship with every other crystal, compute the attention scores using the softmax function, and derive the final weighted pattern for each crystal. This Problem was made with the help of GroundZero AI
    Example:
    Input:

    number of crystals: 5
    values: 4 2 7 1 9
     dimension: 1

    Output:

    [8.9993, 8.9638, 9.0, 8.7259, 9.0]

    Reasoning:

    The self-attention mechanism calculates relationships (attention scores) for each crystal using the given formula. These scores are converted to probabilities using the softmax function, and the final weighted pattern for each crystal is derived by summing the weighted values.
    """
	crystal_values = np.array(crystal_values)
	
	# Calculate scores for self-attention (dot product of each value with every other value)
	scores = np.outer(crystal_values, crystal_values)
	
	# Apply softmax along each row to get attention weights
	attention_weights = np.apply_along_axis(softmax, 1, scores)
	
	# Calculate the final weighted pattern by taking the dot product of weights and values
	output = np.dot(attention_weights, crystal_values)
	
	# Rounding to 4 decimal places to match the example output's precision
	return np.round(output, 4)
