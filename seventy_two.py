
import numpy as np

def jaccard_index(y_true, y_pred):
	"""implement a function jaccard_index(y_true, y_pred) that calculates the Jaccard Index, a measure of similarity between two binary sets. The Jaccard Index is widely used in binary classification tasks to evaluate the overlap between predicted and true labels.
    Your Task:

    Implement the function jaccard_index(y_true, y_pred) to:

        Calculate the Jaccard Index between the arrays y_true and y_pred.
        Return the Jaccard Index as a float value.
        Ensure the function handles cases where:
            There is no overlap between y_true and y_pred.
            Both arrays contain only zeros (edge cases).

    The Jaccard Index is defined as:
    Jaccard Index=Number of elements in the intersection of ytrue and ypredNumber of elements in the union of ytrue and ypred
    Jaccard Index=Number of elements in the union of ytrue​ and ypred​Number of elements in the intersection of ytrue​ and ypred​​

    Where:

        ytrueytrue​ and ypredypred​ are binary arrays of the same length, representing true and predicted labels.
        The result ranges from 0 (no overlap) to 1 (perfect overlap).

    Example:
    Input:

    y_true = np.array([1, 0, 1, 1, 0, 1])
    y_pred = np.array([1, 0, 1, 0, 0, 1])
    print(jaccard_index(y_true, y_pred))

    Output:

    0.75

    Reasoning:

    The Jaccard Index is calculated as 3 / 4 = 0.75, indicating a 75% overlap between the true and predicted labels.
    """
	y_true = np.asarray(y_true, dtype=np.bool_)
	y_pred = np.asarray(y_pred, dtype=np.bool_)

	intersection = np.sum(y_true & y_pred)
	union = np.sum(y_true | y_pred)

	if union == 0:
		return 1.0 if intersection == 0 else 0.0

	result = intersection / union
	return np.round(result, 3)
