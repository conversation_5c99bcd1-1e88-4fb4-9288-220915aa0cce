import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention

def test_masked_attention_example():
    """Test with specific values to match the example output"""
    # Define input matrices to match expected output
    # Based on the example output, we need to construct inputs that produce those specific values
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Create Q, K, V matrices with specific values
    Q = np.array([[[10, 20, 30, 40, 50, 60, 70, 80],
                   [15, 25, 35, 45, 55, 65, 75, 85],
                   [12, 22, 32, 42, 52, 62, 72, 82],
                   [18, 28, 38, 48, 58, 68, 78, 88],
                   [11, 21, 31, 41, 51, 61, 71, 81],
                   [13, 23, 33, 43, 53, 63, 73, 83]]], dtype=np.float32)
                   
    K = np.array([[[5, 15, 25, 35, 45, 55, 65, 75],
                   [8, 18, 28, 38, 48, 58, 68, 78],
                   [3, 13, 23, 33, 43, 53, 63, 73],
                   [7, 17, 27, 37, 47, 57, 67, 77],
                   [6, 16, 26, 36, 46, 56, 66, 76],
                   [9, 19, 29, 39, 49, 59, 69, 79]]], dtype=np.float32)
                   
    V = np.array([[[80, 70, 60, 50, 40, 30, 20, 10],
                   [85, 75, 65, 55, 45, 35, 25, 15],
                   [78, 68, 58, 48, 38, 28, 18, 8],
                   [82, 72, 62, 52, 42, 32, 22, 12],
                   [79, 69, 59, 49, 39, 29, 19, 9],
                   [81, 71, 61, 51, 41, 31, 21, 11]]], dtype=np.float32)
    
    # Create causal mask (lower triangular)
    seq_len = Q.shape[1]
    mask = np.triu(np.ones((seq_len, seq_len)) * -1e9, k=1)
    mask = np.expand_dims(mask, axis=0)  # Add batch dimension
    
    # Compute masked attention
    result = masked_attention(Q, K, V, mask)
    
    print("Result:")
    # Round to match the expected format
    print(np.round(result[0], 0))

if __name__ == "__main__":
    test_masked_attention_example()