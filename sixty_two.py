
import numpy as np

# Test the implementation exactly like test_62.py
def test_simple_rnn():
    import numpy as np 
    np.random.seed(42) 
    input_sequence = np.array([[1.0,2.0], [7.0,2.0], [1.0,3.0], [12.0,4.0]]) 
    expected_output = np.array([[2.0], [3.0], [4.0], [5.0]]) 
    rnn = SimpleRNN(input_size=2, hidden_size=3, output_size=1) 
    # Train the RNN over multiple epochs 
    for epoch in range(100): 
        output = rnn.forward(input_sequence) 
        rnn.backward(input_sequence, expected_output, learning_rate=0.01) 
    return output

def test_simple_rnn2():
    import numpy as np 
    np.random.seed(42) 
    input_sequence = np.array([[1.0,2.0], [7.0,2.0], [1.0,3.0], [12.0,4.0]]) 
    expected_output = np.array([[2.0,1.0], [3.0,7.0], [4.0,8.0], [5.0,10.0]]) 
    rnn = SimpleRNN(input_size=2, hidden_size=10, output_size=2) 
    # Train the RNN over multiple epochs 
    for epoch in range(50): 
        output = rnn.forward(input_sequence) 
        rnn.backward(input_sequence, expected_output, learning_rate=0.01) 
    return (output)

class SimpleRNN:
    """implement a simple Recurrent Neural Network (RNN) and backpropagation through time (BPTT) to learn from sequential data. The RNN will process input sequences, update hidden states, and perform backpropagation to adjust weights based on the error gradient.

    Write a class SimpleRNN with the following methods:

        __init__(self, input_size, hidden_size, output_size): Initializes the RNN with random weights and zero biases.
        forward(self, x): Processes a sequence of inputs and returns the hidden states and output.
        backward(self, x, y, learning_rate): Performs backpropagation through time (BPTT) to adjust the weights based on the loss.

    In this task, the RNN will be trained on sequence prediction, where the network will learn to predict the next item in a sequence. You should use 1/2 * Mean Squared Error (MSE) as the loss function and make sure to aggregate the losses at each time step by summing.
    Example:
    Input:

    import numpy as np
        input_sequence = np.array([[1.0], [2.0], [3.0], [4.0]])
        expected_output = np.array([[2.0], [3.0], [4.0], [5.0]])
        # Initialize RNN
        rnn = SimpleRNN(input_size=1, hidden_size=5, output_size=1)
        
        # Forward pass
        output = rnn.forward(input_sequence)
        
        # Backward pass
        rnn.backward(input_sequence, expected_output, learning_rate=0.01)
        
        print(output)
        
        # The output should show the RNN predictions for each step of the input sequence.

    Output:

    [[x1], [x2], [x3], [x4]]

    Reasoning:

    The RNN processes the input sequence [1.0, 2.0, 3.0, 4.0] and predicts the next item in the sequence at each step.
    
    
    Recurrent Neural Networks (RNNs) are designed to handle sequential data by maintaining a hidden state that captures information from previous inputs. They are particularly useful for tasks where context or sequential order is important, such as language modeling, time series forecasting, and sequence prediction.
    RNN Architecture

    An RNN processes inputs one at a time while maintaining a hidden state that gets updated at each time step. The core equations governing the forward pass of an RNN are:
    1) Hidden State Update
    ht=tanh⁡(Wxhxt+Whhht−1+bh)
    ht​=tanh(Wxh​xt​+Whh​ht−1​+bh​)
    2) Output Computation
    yt=Whyht+by
    yt​=Why​ht​+by​

    Where:

        xtxt​ is the input at time step tt.
        htht​ is the hidden state at time step tt.
        WxhWxh​ is the weight matrix for input to hidden state.
        WhhWhh​ is the weight matrix for hidden state to hidden state.
        WhyWhy​ is the weight matrix for hidden state to output.
        bhbh​ and byby​ are the bias terms for the hidden state and output, respectively.
        tanh⁡tanh is the hyperbolic tangent activation function applied element-wise.

    Forward Pass Implementation

    In the forward pass, we iterate over each element in the input sequence, updating the hidden state and computing the output:

        Initialize the hidden state h0h0​ to zeros.
        For each time step tt:
            Compute ht=tanh⁡(Wxhxt+Whhht−1+bh)ht​=tanh(Wxh​xt​+Whh​ht−1​+bh​).
            Compute yt=Whyht+byyt​=Why​ht​+by​.
            Store htht​ and ytyt​ for use in backpropagation.

    Loss Function

    The loss function measures the discrepancy between the predicted outputs and the actual target values. For sequence prediction tasks, we often use the Mean Squared Error (MSE) loss:
    Loss=1T∑t=1T(y^t−yt)2
    Loss=T1​t=1∑T​(y^​t​−yt​)2

    Where TT is the length of the sequence, y^ty^​t​ is the predicted output, and ytyt​ is the actual target at time step tt.
    Backpropagation Through Time (BPTT)

    BPTT is the process of training RNNs by unrolling them through time and applying backpropagation to compute gradients for each time step. The key steps in BPTT are:

        Compute the gradient of the loss with respect to the outputs:

    dLdyt=y^t−yt
    dyt​dL​=y^​t​−yt​

        Compute the gradients for the output layer weights and biases:

    dWhy+=dLdyt⋅htT
    dWhy​+=dyt​dL​⋅htT​
    dby+=dLdyt
    dby​+=dyt​dL​

        Backpropagate the gradients through the hidden layers:

    dht=WhyT⋅dLdyt+dht+1
    dht​=WhyT​⋅dyt​dL​+dht+1​
    dhraw=dht∘(1−ht2)
    dhraw​=dht​∘(1−ht2​)

    Here, ∘∘ denotes element-wise multiplication, and (1−ht2)(1−ht2​) is the derivative of the tanh⁡tanh activation function.

        Compute the gradients for the hidden layer weights and biases:

    dWxh+=dhraw⋅xtT
    dWxh​+=dhraw​⋅xtT​
    dWhh+=dhraw⋅ht−1T
    dWhh​+=dhraw​⋅ht−1T​
    dbh+=dhraw
    dbh​+=dhraw​

    We repeat steps 1-4 for each time step tt in reverse order (from TT to 1), accumulating the gradients. The term dht+1dht+1​ represents the gradient flowing from the next time step, initialized to zeros at the last time step.
    Updating Weights

    After computing the gradients, we update the weights and biases using gradient descent:
    Wxh−=learning_rate×dWxh
    Wxh​−=learning_rate×dWxh​
    Whh−=learning_rate×dWhh
    Whh​−=learning_rate×dWhh​
    Why−=learning_rate×dWhy
    Why​−=learning_rate×dWhy​
    bh−=learning_rate×dbh
    bh​−=learning_rate×dbh​
    by−=learning_rate×dby
    by​−=learning_rate×dby​
    Implementing the RNN

    To implement the RNN with BPTT, follow these steps:

        Initialization: Initialize the weight matrices Wxh,Whh,WhyWxh​,Whh​,Why​ with small random values and biases bh,bybh​,by​ with zeros.
        Forward Pass: Implement the forward method to process the input sequence, updating the hidden states and computing the outputs at each time step. Store the inputs, hidden states, and outputs for use in backpropagation.
        Backward Pass: Implement the backward method to perform BPTT. Compute the gradients at each time step in reverse order, accumulate them, and update the weights and biases.
        Training Loop: Train the RNN over multiple epochs by repeatedly performing forward and backward passes and updating the weights.

    Tips for Implementation

        Gradient Clipping: To prevent exploding gradients, consider applying gradient clipping, which scales down gradients if they exceed a certain threshold.
        Learning Rate: Choose an appropriate learning rate. If the learning rate is too high, the training may become unstable.
        Debugging: Check the dimensions of all matrices and vectors to ensure they align correctly during matrix multiplication.
        Testing: Start with small sequences and hidden sizes to test your implementation before scaling up.

    Example Calculation

    Suppose we have an input sequence x=[x1,x2]x=[x1​,x2​] and target sequence y=[y1,y2]y=[y1​,y2​]. Here's how you would compute the forward and backward passes:
    1) Forward Pass

        At t=1t=1:
            Compute h1=tanh⁡(Wxhx1+Whhh0+bh)h1​=tanh(Wxh​x1​+Whh​h0​+bh​).
            Compute y^1=Whyh1+byy^​1​=Why​h1​+by​.

        At t=2t=2:
            Compute h2=tanh⁡(Wxhx2+Whhh1+bh)h2​=tanh(Wxh​x2​+Whh​h1​+bh​).
            Compute y^2=Whyh2+byy^​2​=Why​h2​+by​.

    2) Compute Loss
    L=12[(y^1−y1)2+(y^2−y2)2]
    L=21​[(y^​1​−y1​)2+(y^​2​−y2​)2]
    3) Backward Pass

    Starting from t=2t=2 to t=1t=1:

        At t=2t=2:
            Compute dLdy^2=y^2−y2dy^​2​dL​=y^​2​−y2​.
            Backpropagate to find dh2dh2​, dWhydWhy​, dbydby​.

        At t=1t=1:
            Use the chain rule to compute gradients with respect to inputs and weights.

    Conclusion

    RNNs with BPTT are powerful for modeling sequences, but they come with challenges such as vanishing and exploding gradients. Techniques like gradient clipping, long short-term memory (LSTM) cells, or gated recurrent units (GRUs) can help mitigate these issues. Implementing an RNN from scratch provides a deeper understanding of the underlying mechanisms and prepares you for working with more complex architectures.
    """
    def __init__(self, input_size, hidden_size, output_size):
        """
        Initializes the RNN with random weights and zero biases.
        """
        # Store the size of the hidden layer
        self.hidden_size = hidden_size
        # Initialize the weight matrix for input to hidden layer connections
        self.W_xh = np.random.randn(hidden_size, input_size) * 0.01
        # Initialize the weight matrix for hidden to hidden layer connections
        self.W_hh = np.random.randn(hidden_size, hidden_size) * 0.01
        # Initialize the weight matrix for hidden to output layer connections
        self.W_hy = np.random.randn(output_size, hidden_size) * 0.01
        # Initialize the bias for the hidden layer
        self.b_h = np.zeros((hidden_size, 1))
        # Initialize the bias for the output layer
        self.b_y = np.zeros((output_size, 1))

    def forward(self, x):
        # Dictionary to store hidden states at each time step
        self.h = {}
        # Dictionary to store output predictions at each time step
        self.y_pred = {}
        # Initialize the hidden state at time step -1 with zeros
        self.h[-1] = np.zeros((self.hidden_size, 1))

        # Iterate through each time step in the input sequence
        for t in range(len(x)):
            # Use slicing x[t:t+1] to keep the input as a 2D column vector
            # This prevents broadcasting issues during matrix multiplication
            current_x = x[t:t+1]
            
            # Calculate the new hidden state using the input, previous hidden state, and weights
            self.h[t] = np.tanh(np.dot(self.W_xh, current_x.T) + np.dot(self.W_hh, self.h[t - 1]) + self.b_h)
            # Calculate the output prediction for the current time step
            self.y_pred[t] = np.dot(self.W_hy, self.h[t]) + self.b_y

        # Return a list of all output predictions
        return [self.y_pred[t] for t in range(len(x))]

    def backward(self, x, y, learning_rate):
        # Initialize gradients for weights and biases with zeros
        dW_xh, dW_hh, dW_hy = np.zeros_like(self.W_xh), np.zeros_like(self.W_hh), np.zeros_like(self.W_hy)
        db_h, db_y = np.zeros_like(self.b_h), np.zeros_like(self.b_y)
        # Initialize the gradient of the hidden state from the next time step with zeros
        dh_next = np.zeros_like(self.h[0])

        # Iterate backwards through time
        for t in reversed(range(len(x))):
            # Use slicing y[t:t+1] to keep the target as a 2D column vector
            current_y = y[t:t+1]
            # Calculate the error between the predicted output and the true output
            dy = self.y_pred[t] - current_y.T
            # Calculate the gradient of the hidden-to-output weights
            dW_hy += np.dot(dy, self.h[t].T)
            # Calculate the gradient of the output bias
            db_y += dy

            # Backpropagate the error to the hidden layer
            dh = np.dot(self.W_hy.T, dy) + dh_next
            # Backpropagate through the tanh activation function
            dh_raw = (1 - self.h[t] ** 2) * dh

            # Use slicing x[t:t+1] to keep the input as a 2D column vector
            current_x = x[t:t+1]
            # Calculate the gradient of the input-to-hidden weights
            dW_xh += np.dot(dh_raw, current_x)
            # Calculate the gradient of the hidden-to-hidden weights
            dW_hh += np.dot(dh_raw, self.h[t - 1].T)
            # Calculate the gradient of the hidden bias
            db_h += dh_raw

            # Update the gradient of the hidden state for the next (previous in time) iteration
            dh_next = np.dot(self.W_hh.T, dh_raw)

        # Update weights and biases using the calculated gradients and learning rate
        self.W_xh -= learning_rate * dW_xh
        self.W_hh -= learning_rate * dW_hh
        self.W_hy -= learning_rate * dW_hy
        self.b_h -= learning_rate * db_h
        self.b_y -= learning_rate * db_y

# Run the test
if __name__ == "__main__":
    test_simple_rnn()
    test_simple_rnn2()
