import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

def diagnose_issue():
    """Diagnose the issue with the test case"""
    print("Diagnosing the issue...")
    
    # Set the random seed for reproducibility
    np.random.seed(42)
    
    # Create input matrix X
    X = np.arange(48).reshape(6, 8)
    X = np.random.permutation(X.flatten()).reshape(6, 8)
    print("X shape:", X.shape)
    
    # Create mask
    mask = np.triu(np.ones((6, 6)) * (-np.inf), k=1)
    print("mask shape:", mask.shape)
    
    # Create weight matrices
    W_q = np.random.randint(0, 4, size=(8, 8))
    W_k = np.random.randint(0, 5, size=(8, 8))
    W_v = np.random.randint(0, 6, size=(8, 8))
    print("W_q shape:", W_q.shape)
    print("W_k shape:", W_k.shape)
    print("W_v shape:", W_v.shape)
    
    # Compute Q, K, V
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    
    # Print some values to verify
    print("\nFirst few values:")
    print("Q[0, :3]:", Q[0, :3])
    print("K[0, :3]:", K[0, :3])
    print("V[0, :3]:", V[0, :3])
    
    # Add batch dimension as required by masked_attention
    Q_batch = Q.reshape(1, *Q.shape)
    K_batch = K.reshape(1, *K.shape)
    V_batch = V.reshape(1, *V.shape)
    print("\nAfter reshaping for batch dimension:")
    print("Q_batch shape:", Q_batch.shape)
    print("K_batch shape:", K_batch.shape)
    print("V_batch shape:", V_batch.shape)
    
    # Try to replicate the exact call that's failing
    try:
        print("\nCalling masked_attention...")
        result = masked_attention(Q_batch, K_batch, V_batch, mask)
        print("Success! Result shape:", result.shape)
        print("Result (rounded):")
        print(np.round(result[0]).astype(int))
    except Exception as e:
        print("Error:", str(e))
        import traceback
        traceback.print_exc()
        
        # Let's manually check the matrix multiplication that might be failing
        print("\nManual verification of matrix operations:")
        try:
            print("Q_batch:", Q_batch.shape)
            print("K_batch transpose:", K_batch.transpose(0, 2, 1).shape)
            scores = np.matmul(Q_batch, K_batch.transpose(0, 2, 1))
            print("Scores shape:", scores.shape)
            print("Scores sample:", scores[0, :2, :2])
        except Exception as e2:
            print("Matrix multiplication failed:", str(e2))
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    diagnose_issue()
