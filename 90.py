import numpy as np
from collections import Counter
import math

def calculate_bm25_scores(corpus, query, k1=1.5, b=0.75):
	"""Implement the BM25 ranking function to calculate document scores for a query in an information retrieval context. BM25 is an advanced variation of TF-IDF that incorporates term frequency saturation, document length normalization, and a configurable penalty for document length effects.
    Example:
    Input:

    corpus = [['the', 'cat', 'sat'], ['the', 'dog', 'ran'], ['the', 'bird', 'flew']], query = ['the', 'cat']

    Output:

    [0.693, 0., 0. ]

    Reasoning:

    BM25 calculates scores for each document in the corpus by evaluating how well the query terms match each document while considering term frequency saturation and document length normalization.
    """
	N = len(corpus)
	if N == 0:
		return np.array([])

	avgdl = sum(len(doc) for doc in corpus) / N
	
	unique_query_terms = set(query)
	doc_freqs = {term: sum(1 for doc in corpus if term in doc) for term in unique_query_terms}
	
	idf = {}
	for term, n_q in doc_freqs.items():
		if n_q > 0 and n_q < N:
			idf[term] = math.log((N+1) / (n_q+1))
		else:
			idf[term] = 0

	scores = []
	for doc in corpus:
		doc_len = len(doc)
		term_freqs = Counter(doc)
		
		doc_score = 0.0
		for term in query:
			term_idf = idf.get(term, 0)
			
			if term_idf > 0 and term in term_freqs:
				tf = term_freqs[term]
				
				numerator = term_idf * tf * (k1 + 1)
				denominator = tf + k1 * (1 - b + b * (doc_len / avgdl))
				
				if denominator != 0:
					doc_score += numerator / denominator
					
		scores.append(doc_score)
		
	scores = np.array(scores)
	return np.round(scores, 3)