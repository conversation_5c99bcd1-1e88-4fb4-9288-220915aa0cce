import numpy as np

def train_logreg(X: np.n<PERSON><PERSON>, y: np.n<PERSON>ray, learning_rate: float, iterations: int) -> tuple[list[float], ...]:
	"""
	Gradient-descent training algorithm for logistic regression, optimizing parameters with Binary Cross Entropy loss.
    
	Implement a gradient descent-based training algorithm for logistic regression. Your task is to compute model parameters using Binary Cross Entropy loss and return the optimized coefficients along with collected loss values over iterations(round to the 4th decimal).
    Example:
    Input:

    train_logreg(np.array([[1.0, 0.5], [-0.5, -1.5], [2.0, 1.5], [-2.0, -1.0]]), np.array([1, 0, 1, 0]), 0.01, 20)

    Output:

    ([0.0037, 0.0246, 0.0202], [2.7726, 2.7373, 2.7024, 2.6678, 2.6335, 2.5995, 2.5659, 2.5327, 2.4997, 2.4671, 2.4348, 2.4029, 2.3712, 2.3399, 2.3089, 2.2783, 2.2480, 2.2180, 2.1882, 2.1588])

    Reasoning:

    The function iteratively updates the logistic regression parameters using gradient descent and collects loss values over iterations.

    Logistic regression is a model used for a binary classification poblem.
    Prerequisites for a regular logistic regression

    Logistic regression is based on the concept of "logits of odds". Odds is measure of how frequent we encounter success. It also allows us to shift our probabilities domain of [0,1][0,1] to [0,∞][0,∞] Consider a probability of scoring a goal p=0.8p=0.8, then our odds=0.80.2=4odds=0.20.8​=4. This means that every 44 matches we could be expecting a goal followed by a miss. So the higher the odds, the more consistent is our streak of goals. Logit is an inverse of the standard logistic function, i.e. sigmoid: logit(p)=σ−1(p)=lnp1−plogit(p)=σ−1(p)=ln1−pp​. In our case pp is a probability, therefore we call p1−p1−pp​ the "odds". The logit allows us to further expand our domain from [0,∞][0,∞] to [−∞,∞][−∞,∞].

    With this domain expansion we can treat our problem as a linear regression and try to approximate our logit function: Xβ=logit(p)Xβ=logit(p). However what we really want for this approximation is to yield predictions for probabilities:
    Xβ=lnp1−pe−Xβ=1−ppe−Xβ+1=1pp=1e−Xβ+1
    Xβ=ln1−pp​e−Xβ=p1−p​e−Xβ+1=p1​p=e−Xβ+11​

    What we practically just did is taking an inverse of a logit function w.r.t. our approximation and go back to sigmoid. This is also the backbone of the regular logistic regression, which is commonly defined as:
    π=eα+Xβ1+eα+Xβ=11+e−(α+Xβ).
    π=1+eα+Xβeα+Xβ​=1+e−(α+Xβ)1​.
    Loss in logistic regression

    The loss function used for solving the logistic regression for ββ is derived from MLE (Maximum Likelihood Estimation). This method allows us to search for ββ that maximize our likelihood function L(β)L(β). This function tells us how likely it is that XX has come from the distribution generated by ββ: L(β)=L(β∣X)=P(X∣β)=∏{x∈X}fXunivar(x;β)L(β)=L(β∣X)=P(X∣β)=∏{x∈X}​fXunivar​(x;β), where ff is a PMF and univarunivar means univariate, i.e. applied to a single variable.

    In the case of a regular logistic regression we expect our output to belong to a single Bernoulli-distributed random variable (hence the univariance), since our true label is either yi=0yi​=0 or yi=1yi​=1. The Bernoulli's PMF is defined as P(Y=y)=py(1−p)(1−y)P(Y=y)=py(1−p)(1−y), where y∈{0,1}y∈{0,1}. Also let's denote {x∈X}{x∈X} simply as XX and refer to a single pair of vectors from the training set as (xi,yi)(xi​,yi​). Thus, our likelihood function would look like this:
    ∏Xp(xi)yi×[1−p(xi)]1−yi
    X∏​p(xi​)yi​×[1−p(xi​)]1−yi​

    Then we convert our function from likelihood to log-likelihood by taking lnln (or loglog) of it:
    ∑Xyilog⁡[p(xi)]+(1−yi)log⁡[1−p(xi)]
    X∑​yi​log[p(xi​)]+(1−yi​)log[1−p(xi​)]

    And then we replace p(xi)p(xi​) with the sigmoid from previously defined equality to get a final version of our loss function:
    ∑Xyilog⁡(11+e−xiβ)+(1−yi)log⁡(1−11+e−xiβ)
    X∑​yi​log(1+e−xi​β1​)+(1−yi​)log(1−1+e−xi​β1​)
    Optimization objective

    Recall that originally we wanted to search for ββ that maximize the likelihood function. Since loglog is a monotonic transformation, our maximization objective does not change and we can confindently say that now we can equally search for ββ that maximize our log-likelihood. Hence we can finally write our actual objective as:
    argmaxβ[∑Xyilog⁡σ(xiβ)+(1−yi)log⁡(1−σ(xiβ))]==argminβ−[∑Xyilog⁡σ(xiβ)+(1−yi)log⁡(1−σ(xiβ))]
    argmaxβ​[X∑​yi​logσ(xi​β)+(1−yi​)log(1−σ(xi​β))]==argminβ​−[X∑​yi​logσ(xi​β)+(1−yi​)log(1−σ(xi​β))]

    where σσ is the sigmoid. This function we're trying to minimize is also called Binary Cross Entropy loss function (BCE). To find the minimum we would need to take the gradient of this LLF (Log-Likelihood Function), or find a vector of derivatives with respect to every individual βjβj​.
    Step 1

    To do that we're going to use a chain rule, that describes relational change in variables that our original function is made of. In our case the log-likeligood function depends on sigmoid σσ, σσ depends on XβXβ and XβXβ finally depends on βjβj​, hence:
    ∂LLF∂βj=∂LLF∂σ∂σ∂[Xβ]∂[Xβ]βj=−∑i=1n(y(i)1σ(x(i)β)−(1−y(i))11−σ(x(i)β))∂σ∂[x(i)β]
    ∂βj​∂LLF​=∂σ∂LLF​∂[Xβ]∂σ​βj​∂[Xβ]​=−i=1∑n​(y(i)σ(x(i)β)1​−(1−y(i))1−σ(x(i)β)1​)∂[x(i)β]∂σ​
    Step 2

    Then we use a derivative of the sigmoid function, that is ∂σ(x)∂x=σ(x)(1−σ(x))∂x∂σ(x)​=σ(x)(1−σ(x)):
    −∑i=1n(y(i)1σ(x(i)β)−(1−y(i))11−σ(x(i)β))σ(x(i)β)(1−σ(x(i)β))(∗)∂[x(i)β]∂βj=−∑i=1n(y(i)(1−σ(x(i)β))−(1−y(i))σ(x(i)β))xj(i)=−∑i=1n(y(i)−σ(x(i)β))xj(i)=∑i=1n(σ(x(i)β)−y(i))xj(i).
    −i=1∑n​(y(i)σ(x(i)β)1​−(1−y(i))1−σ(x(i)β)1​)σ(x(i)β)(1−σ(x(i)β))(∗)∂βj​∂[x(i)β]​=−i=1∑n​(y(i)(1−σ(x(i)β))−(1−y(i))σ(x(i)β))xj(i)​=−i=1∑n​(y(i)−σ(x(i)β))xj(i)​=i=1∑n​(σ(x(i)β)−y(i))xj(i)​.

    The result sum can be then rewritten in a more convenient gradient matrix form as:
    XT(σ(Xβ)−Y)
    XT(σ(Xβ)−Y)

    Then we can finally use gradient descent in order to iteratively update our parameters:
    βt+1=βt−η[XT(σ(Xβt)−Y)]
    βt+1​=βt​−η[XT(σ(Xβt​)−Y)]
	"""
	# Your code here
	n_samples, n_features = X.shape
	X_b = np.c_[np.ones((n_samples, 1)), X]
	beta = np.zeros(n_features + 1)
	loss_history = []
	epsilon = 1e-9

	def sigmoid(z):
		return 1 / (1 + np.exp(-z))

	for _ in range(iterations):
		z = np.dot(X_b, beta)
		p = sigmoid(z)
		
		loss = -np.sum(y * np.log(p + epsilon) + (1 - y) * np.log(1 - p + epsilon))
		loss_history.append(round(loss, 4))
		
		gradient = np.dot(X_b.T, (p - y))
		beta -= learning_rate * gradient
		
	final_beta = np.round(beta, 4).tolist()
	
	return final_beta, loss_history