import unittest
import numpy as np
import importlib.util

# Load the train_softmaxreg function from 105.py
spec = importlib.util.spec_from_file_location("module.name", "./105.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)
train_softmaxreg = module.train_softmaxreg

class TestTrainSoftmaxReg(unittest.TestCase):
    def test_train_softmaxreg(self):
        # Test case from the example in the docstring
        X = np.array([[0.5, -1.2], [-0.3, 1.1], [0.8, -0.6]])
        y = np.array([0, 1, 2])

        # Expected output is approximate, as the values may vary slightly due to numerical precision
        expected_theta = [
            [-0.0011, 0.0145, -0.0921],
            [0.002, -0.0598, 0.1263],
            [-0.0009, 0.0453, -0.0342]
        ]
        expected_losses = [3.2958, 3.2611, 3.2272, 3.1941, 3.1618, 3.1302, 3.0993, 3.0692, 3.0398, 3.011]

        # Run the function with a smaller number of iterations for faster testing
        theta, losses = train_softmaxreg(X, y, 0.01, 5)

        # Check shapes
        self.assertEqual(len(theta), len(expected_theta))
        self.assertEqual(len(theta[0]), len(expected_theta[0]))
        self.assertEqual(len(losses), 5)

        # Check that losses are decreasing (general trend)
        self.assertTrue(all(losses[i] >= losses[i+1] for i in range(len(losses)-1)))

if __name__ == '__main__':
    unittest.main()