
import numpy as np
from sixty_nine import r_squared

def test_r_squared():
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([1.1, 2.1, 2.9, 4.2, 4.8])
    assert np.isclose(r_squared(y_true, y_pred), 0.989)

def test_r_squared_perfect_fit():
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([1, 2, 3, 4, 5])
    assert np.isclose(r_squared(y_true, y_pred), 1.0)

def test_r_squared_no_fit():
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([3, 3, 3, 3, 3])
    assert np.isclose(r_squared(y_true, y_pred), 0.0)

def test_r_squared_negative_r_squared():
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([5, 4, 3, 2, 1])
    assert np.isclose(r_squared(y_true, y_pred), -3.0)
