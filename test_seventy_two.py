
import numpy as np
from seventy_two import jaccard_index

def test_jaccard_index_valid():
    y_true = np.array([1, 0, 1, 1, 0, 1])
    y_pred = np.array([1, 0, 1, 0, 0, 1])
    assert jaccard_index(y_true, y_pred) == 0.75

def test_jaccard_index_no_overlap():
    y_true = np.array([1, 1, 1])
    y_pred = np.array([0, 0, 0])
    assert jaccard_index(y_true, y_pred) == 0.0

def test_jaccard_index_all_zeros():
    y_true = np.array([0, 0, 0])
    y_pred = np.array([0, 0, 0])
    assert jaccard_index(y_true, y_pred) == 1.0

def test_jaccard_index_perfect_overlap():
    y_true = np.array([1, 1, 1])
    y_pred = np.array([1, 1, 1])
    assert jaccard_index(y_true, y_pred) == 1.0
