from collections import Counter

def confusion_matrix(data):
	""" implement the function confusion_matrix(data) that generates a confusion matrix for a binary classification problem. The confusion matrix provides a summary of the prediction results on a classification problem, allowing you to visualize how many data points were correctly or incorrectly labeled.
    Input:

        A list of lists, where each inner list represents a pair
        [y_true, y_pred] for one observation. y_true is the actual label, and y_pred is the predicted label.

    Output:

        A 2×2 confusion matrix represented as a list of lists.

    Example:
    Input:

    data = [[1, 1], [1, 0], [0, 1], [0, 0], [0, 1]]
    print(confusion_matrix(data))

    Output:

    [[1, 1], [2, 1]]

    Reasoning:

    The confusion matrix shows the counts of true positives, false negatives, false positives, and true negatives.
    Learn About topic
    """
	tp = sum(1 for y_true, y_pred in data if y_true == 1 and y_pred == 1)
	fn = sum(1 for y_true, y_pred in data if y_true == 1 and y_pred == 0)
	fp = sum(1 for y_true, y_pred in data if y_true == 0 and y_pred == 1)
	tn = sum(1 for y_true, y_pred in data if y_true == 0 and y_pred == 0)
	return [[tp, fn], [fp, tn]]