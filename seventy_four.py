
import numpy as np

def create_row_hv(row, dim, random_seeds):
    if not isinstance(row, dict) or not isinstance(random_seeds, dict):
        raise TypeError("Inputs 'row' and 'random_seeds' must be dictionaries.")

    bundled_hv = np.zeros(dim)

    for feature, value in row.items():
        if feature not in random_seeds:
            raise ValueError(f"No random seed provided for feature: {feature}")

        # 1. Create hypervector for the feature name
        feature_seed = random_seeds[feature]
        rng_feature = np.random.RandomState(feature_seed)
        feature_hv = rng_feature.randint(0, 2, dim) * 2 - 1

        # 2. Create hypervector for the feature value
        #rng_value = np.random.RandomState(feature_seed)
        value_hv = rng_feature.randint(0, 2, dim) * 2 - 1

        # 3. Bind feature and value hypervectors
        bound_hv = feature_hv * value_hv

        # 4. Add to the bundle
        bundled_hv += bound_hv

    # 5. Normalize the bundled vector
    final_row_hv = np.sign(bundled_hv)
    final_row_hv[final_row_hv == 0] = 1  # Replace zeros with 1

    return final_row_hv.astype(int)
