import pytest
import importlib.util

# Load the module from the file path
spec = importlib.util.spec_from_file_location("module_65", "/mnt/e/work/playground/ultimate_ai/deep-ml/65.py")
module_65 = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module_65)
compressed_row_sparse_matrix = module_65.compressed_row_sparse_matrix

def test_compressed_row_sparse_matrix():
    dense_matrix = [
        [1, 0, 0, 0],
        [0, 2, 0, 0],
        [3, 0, 4, 0],
        [1, 0, 0, 5]
    ]
    vals, col_idx, row_ptr = compressed_row_sparse_matrix(dense_matrix)
    assert vals == [1, 2, 3, 4, 1, 5]
    assert col_idx == [0, 1, 0, 2, 0, 3]
    assert row_ptr == [0, 1, 2, 4, 6]

def test_compressed_row_sparse_matrix_empty():
    dense_matrix = [[]]
    vals, col_idx, row_ptr = compressed_row_sparse_matrix(dense_matrix)
    assert vals == []
    assert col_idx == []
    assert row_ptr == [0, 0]

def test_compressed_row_sparse_matrix_all_zeros():
    dense_matrix = [
        [0, 0, 0],
        [0, 0, 0],
        [0, 0, 0]
    ]
    vals, col_idx, row_ptr = compressed_row_sparse_matrix(dense_matrix)
    assert vals == []
    assert col_idx == []
    assert row_ptr == [0, 0, 0, 0]
