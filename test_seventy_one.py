
import numpy as np
from seventy_one import rmse

def test_rmse_valid():
    y_true = np.array([3, -0.5, 2, 7])
    y_pred = np.array([2.5, 0.0, 2, 8])
    assert rmse(y_true, y_pred) == 0.612

def test_rmse_empty():
    y_true = []
    y_pred = []
    assert rmse(y_true, y_pred) == 0.0

def test_rmse_mismatched_shape():
    y_true = np.array([1, 2, 3])
    y_pred = np.array([1, 2])
    assert rmse(y_true, y_pred) == -1

def test_rmse_invalid_input():
    y_true = "a"
    y_pred = [1, 2]
    assert rmse(y_true, y_pred) == -1
