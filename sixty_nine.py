import numpy as np

def r_squared(y_true, y_pred):
	"""
    R-squared, also known as the coefficient of determination, is a measure that indicates how well the independent variables explain the variability of the dependent variable in a regression model.

        Your Task: To implement the function r_squared(y_true, y_pred) that calculates the R-squared value, given arrays of true values y_true and predicted values y_pred.

    Example:
    Input:

    import numpy as np

    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([1.1, 2.1, 2.9, 4.2, 4.8])
    print(r_squared(y_true, y_pred))

    Output:

    0.989

    Reasoning:

    The R-squared value is calculated to be 0.989, indicating that the regression model explains 98.9% of the variance in the dependent variable.
	"""
	y_true = np.array(y_true)
	y_pred = np.array(y_pred)

	ss_res = np.sum((y_true - y_pred) ** 2)
	ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)

	if ss_tot == 0:
		return 1.0 if ss_res == 0 else 0.0

	r2 = 1 - (ss_res / ss_tot)
	return r2