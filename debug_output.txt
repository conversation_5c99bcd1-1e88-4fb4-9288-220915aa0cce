X shape: (6, 8)
X:
 [[27 40 26 43 24 37 12 19]
 [ 4 25  8  3  6 39 33 13]
 [17 45 15  9 16 29 32 46]
 [ 0 31 30  5 11 34  1 44]
 [21  2 36 35 23 41 10 22]
 [18 47 20  7 42 14 28 38]]
mask shape: (6, 6)
mask:
 [[  0. -inf -inf -inf -inf -inf]
 [  0.   0. -inf -inf -inf -inf]
 [  0.   0.   0. -inf -inf -inf]
 [  0.   0.   0.   0. -inf -inf]
 [  0.   0.   0.   0.   0. -inf]
 [  0.   0.   0.   0.   0.   0.]]
W_q shape: (8, 8)
W_k shape: (8, 8)
W_v shape: (8, 8)
Q shape: (6, 8)
K shape: (6, 8)
V shape: (6, 8)
Q:
 [[432 381 409 366 336 429 420 288]
 [300 157 254 300 178 338 180 183]
 [392 286 435 376 267 481 377 298]
 [329 214 370 257 199 405 315 307]
 [412 263 315 294 213 389 298 324]
 [330 340 418 345 320 443 370 279]]
K:
 [[570 504 491 531 304 449 307 408]
 [357 344 315 311 185 369 285 292]
 [622 434 417 529 265 539 363 428]
 [427 284 284 307 176 376 360 349]
 [473 411 400 491 235 371 358 342]
 [701 418 350 553 294 498 286 382]]
V:
 [[547 490 399 495 485 439 645 393]
 [203 293 199 399 220 163 279 174]
 [471 472 429 538 377 450 531 362]
 [398 388 244 338 224 385 413 295]
 [541 331 366 492 475 367 423 422]
 [500 463 514 447 403 500 556 496]]

After reshaping:
Q shape: (1, 6, 8)
K shape: (1, 6, 8)
V shape: (1, 6, 8)
mask shape: (6, 6)
mask_with_batch shape: (1, 6, 6)
Result shape: (1, 6, 8)
Actual result:
[[547 490 399 495 485 439 645 393]
 [547 490 399 495 485 439 645 393]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]]

Expected result:
[[547 490 399 495 485 439 645 393]
 [547 490 399 495 485 439 645 393]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]]

Match: True
Result shape: (1, 6, 8)
Actual result with non-batch mask:
[[547 490 399 495 485 439 645 393]
 [547 490 399 495 485 439 645 393]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]
 [471 472 429 538 377 450 531 362]]
