import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

# Exact replication of the failing test case
np.random.seed(42)
X = np.arange(48).reshape(6,8)
X = np.random.permutation(X.flatten()).reshape(6, 8)
mask = np.triu(np.ones((6, 6))*(-np.inf), k=1)
W_q = np.random.randint(0,4,size=(8,8))
W_k = np.random.randint(0,5,size=(8,8))
W_v = np.random.randint(0,6,size=(8,8))
Q, K, V = compute_qkv(X, W_q, W_k, W_v)

# Add batch dimension as required by masked_attention
Q = Q.reshape(1, *Q.shape)
K = K.reshape(1, *K.shape)
V = V.reshape(1, *V.shape)

# This should work
result = masked_attention(Q, K, V, mask)
print(np.round(result[0]).astype(int))