import unittest
from sixty_six import orthogonal_projection

class TestOrthogonalProjection(unittest.TestCase):

    def test_example_case(self):
        v = [3, 4]
        L = [1, 0]
        self.assertEqual(orthogonal_projection(v, L), [3.0, 0.0])

    def test_another_case(self):
        v = [1, 1, 1]
        L = [2, 0, 0]
        self.assertEqual(orthogonal_projection(v, L), [1.0, 0.0, 0.0])

    def test_zero_vector_v(self):
        v = [0, 0, 0]
        L = [1, 2, 3]
        self.assertEqual(orthogonal_projection(v, L), [0.0, 0.0, 0.0])

    def test_zero_vector_L(self):
        v = [1, 2, 3]
        L = [0, 0, 0]
        self.assertEqual(orthogonal_projection(v, L), [0.0, 0.0, 0.0])

if __name__ == '__main__':
    unittest.main()
