import numpy as np

def grpo_objective(rhos, A, pi_theta_old, pi_theta_ref, epsilon=0.2, beta=0.01) -> float:
	"""
    Implement the GRPO (Generalized Relative Policy Optimization) objective function used to optimize policy parameters in reinforcement learning.

    GRPO Objective: J_GRPO(θ) = E[1/G * Σ min(ρ_i * A_i, clip(ρ_i, 1-ε, 1+ε) * A_i) - β * D_KL(π_θ || π_ref)]

    Where:
    - ρ_i = π_θ(o_i|q) / π_θ_old(o_i|q) is the likelihood ratio
    - A_i is the advantage estimate for the i-th action
    - ε is the clipping parameter
    - β controls the influence of the KL divergence penalty
    - D_KL is the Kullback-Leibler divergence between current policy π_θ and reference policy π_ref

    Example:
    Input:
    grpo_objective([1.2, 0.8, 1.1], [1.0, 1.0, 1.0], [0.9, 1.1, 1.0], [1.0, 0.5, 1.5], epsilon=0.2, beta=0.01)

    Output:
    1.032749 (Note: The exact expected value may use slightly different rounding)

    Args:
		rhos: List of likelihood ratios (ρ_i) = π_θ(o_i | q) / π_θ_old(o_i | q).
		A: List of advantage estimates (A_i).
		pi_theta_old: List representing the old policy probabilities π_θ_old(o_i | q).
		pi_theta_ref: List representing the reference policy probabilities π_ref(o_i | q).
		epsilon: Clipping parameter (ε).
		beta: KL divergence penalty coefficient (β).

	Returns:
		The computed GRPO objective value.
		
		
    Understanding GRPO (Generalized Relative Policy Optimization)

    GRPO is an advanced policy optimization algorithm in reinforcement learning that updates policy parameters while ensuring training stability. It builds upon Proximal Policy Optimization (PPO) by incorporating a KL divergence penalty to keep the new policy close to a reference policy.
    Mathematical Definition

    The GRPO objective function is defined as:
    JGRPO(θ)=Eq∼P(Q),{oi}i=1G∼πθold(O∣q)[1G∑i=1Gmin⁡(ρiAi,clip(ρi,1−ϵ,1+ϵ)Ai)−βDKL(πθ∥πref)]
    JGRPO​(θ)=Eq∼P(Q),{oi​}i=1G​∼πθold​​(O∣q)​[G1​i=1∑G​min(ρi​Ai​,clip(ρi​,1−ϵ,1+ϵ)Ai​)−βDKL​(πθ​∥πref​)]

    Where:

        ρi=πθ(oi∣q)πθold(oi∣q)ρi​=πθold​​(oi​∣q)πθ​(oi​∣q)​ is the likelihood ratio.
        AiAi​ is the advantage estimate for the ii-th action.
        ϵϵ is the clipping parameter.
        ββ controls the influence of the KL divergence penalty.
        DKLDKL​ is the Kullback-Leibler divergence between the new policy πθπθ​ and the reference policy πrefπref​.

    Key Components
    Likelihood Ratio ρiρi​

        Measures how much more likely the new policy πθπθ​ is to produce an output oioi​ compared to the old policy πθoldπθold​​.
        ρi=πθ(oi∣q)πθold(oi∣q)ρi​=πθold​​(oi​∣q)πθ​(oi​∣q)​

    Advantage Function AiAi​

        Evaluates the benefit of taking action oioi​ compared to the average action.
        Ai=ri−mean(r1,…,rG)std(r1,…,rG)Ai​=std(r1​,…,rG​)ri​−mean(r1​,…,rG​)​
        Where riri​ is the reward for the ii-th action.

    Clipping Mechanism

        Restricts the likelihood ratio to the range [1−ϵ,1+ϵ][1−ϵ,1+ϵ] to prevent large updates.
        clip(ρi,1−ϵ,1+ϵ)clip(ρi​,1−ϵ,1+ϵ)

    KL Divergence Penalty

        Ensures the new policy πθπθ​ does not deviate significantly from the reference policy πrefπref​.
        −βDKL(πθ∥πref)−βDKL​(πθ​∥πref​)

    Benefits of GRPO
    Stability

        The clipping mechanism prevents drastic policy updates, ensuring stable training.

    Controlled Exploration

        The KL divergence penalty maintains a balance between exploring new policies and sticking close to a reliable reference policy.

    Improved Performance

        By carefully managing policy updates, GRPO can lead to more effective learning and better policy performance.

    Use Cases
    Reinforcement Learning Tasks

        Suitable for environments requiring stable and efficient policy updates.
        also a key component used for the DeepSeek-R1 model

    Complex Decision-Making Problems

        Effective in scenarios with high-dimensional action spaces where maintaining policy stability is crucial.

    Conclusion

    GRPO enhances policy optimization in reinforcement learning by combining the benefits of PPO with an additional KL divergence penalty. This ensures that policy updates are both effective and stable, leading to more reliable and performant learning agents.
	"""
	# Convert lists to numpy arrays for vectorized operations
	rhos = np.array(rhos)
	A = np.array(A)
	pi_theta_old = np.array(pi_theta_old)
	pi_theta_ref = np.array(pi_theta_ref)

	# Part 1: Clipped surrogate objective
	unclipped_term = rhos * A
	clipped_rhos = np.clip(rhos, 1 - epsilon, 1 + epsilon)
	clipped_term = clipped_rhos * A
	min_objective = np.minimum(unclipped_term, clipped_term)
	expected_clipped_objective = np.mean(min_objective)

	# Part 2: KL divergence penalty
	# Calculate the current policy pi_theta from the ratio and the old policy
	pi_theta = rhos * pi_theta_old

	# Normalize the policies to be valid probability distributions for KL divergence
	pi_theta_normalized = pi_theta / np.sum(pi_theta)
	pi_theta_ref_normalized = pi_theta_ref / np.sum(pi_theta_ref)
	
	# Add a small epsilon for numerical stability to prevent log(0) or division by zero.
	pi_theta_normalized += 1e-10
	pi_theta_ref_normalized += 1e-10

	# D_KL(pi_theta || pi_theta_ref) = sum(pi_theta * log(pi_theta / pi_theta_ref))
	kl_divergence = np.sum(pi_theta_normalized * np.log(pi_theta_normalized / pi_theta_ref_normalized))

	# Final GRPO objective
	objective = expected_clipped_objective - beta * kl_divergence

	return float(objective)