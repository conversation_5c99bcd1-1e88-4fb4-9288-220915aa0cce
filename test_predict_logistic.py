import unittest
import numpy as np
import importlib.util

# Load the predict_logistic function from 104.py
spec = importlib.util.spec_from_file_location("module.name", "./104.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)
predict_logistic = module.predict_logistic

class TestPredictLogistic(unittest.TestCase):
    def test_predict_logistic(self):
        # Test case from the example in the docstring
        X = np.array([[1, 1], [2, 2], [-1, -1], [-2, -2]])
        weights = np.array([1, 1])
        bias = 0

        expected = np.array([1, 1, 0, 0])
        result = predict_logistic(X, weights, bias)

        np.testing.assert_array_equal(result, expected)

if __name__ == '__main__':
    unittest.main()