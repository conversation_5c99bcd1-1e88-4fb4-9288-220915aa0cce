import pytest
import importlib.util

# Load the module from the file path
spec = importlib.util.spec_from_file_location("module_64", "/mnt/e/work/playground/ultimate_ai/deep-ml/64.py")
module_64 = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module_64)
gini_impurity = module_64.gini_impurity


def test_gini_impurity():
    y = [0, 1, 1, 1, 0]
    assert gini_impurity(y) == 0.48

def test_gini_impurity_empty():
    y = []
    assert gini_impurity(y) == 0.0

def test_gini_impurity_single_class():
    y = [0, 0, 0, 0, 0]
    assert gini_impurity(y) == 0.0

def test_gini_impurity_multiple_classes():
    y = [0, 1, 2, 0, 1, 2]
    # p0 = 2/6, p1 = 2/6, p2 = 2/6
    # 1 - ((2/6)^2 + (2/6)^2 + (2/6)^2) = 1 - 3 * (1/9) = 1 - 1/3 = 2/3 = 0.667
    assert gini_impurity(y) == 0.667
