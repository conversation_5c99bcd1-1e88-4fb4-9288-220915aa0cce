def hard_sigmoid(x: float) -> float:
	"""
    Implement the Hard Sigmoid activation function, a computationally efficient approximation of the standard sigmoid function. Your function should take a single input value and return the corresponding output based on the Hard Sigmoid definition.
    Example:
    Input:

    hard_sigmoid(0.0)

    Output:

    0.5

    Reasoning:

    The input 0.0 falls in the linear region of the Hard Sigmoid function. Using the formula HardSigmoid(x)=0.2x+0.5HardSigmoid(x)=0.2x+0.5, the output is 0.2×0.0+0.5=0.50.2×0.0+0.5=0.5.

	Implements the Hard Sigmoid activation function.

	Args:
		x (float): Input value

	Returns:
		float: The Hard Sigmoid of the input
	"""
	# Hard Sigmoid formula: max(0, min(1, 0.2 * x + 0.5))
	linear_result = 0.2 * x + 0.5
	
	# Clamp the result between 0 and 1
	if linear_result <= 0:
		return 0.0
	elif linear_result >= 1:
		return 1.0
	else:
		return linear_result