import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention, compute_qkv

def test_exact_case():
    """Test with the exact case provided in the problem statement"""
    # Set the random seed for reproducibility
    np.random.seed(42)
    
    # Create input matrix X
    X = np.arange(48).reshape(6, 8)
    X = np.random.permutation(X.flatten()).reshape(6, 8)
    
    # Create mask
    mask = np.triu(np.ones((6, 6)) * (-np.inf), k=1)
    
    # Create weight matrices
    W_q = np.random.randint(0, 4, size=(8, 8))
    W_k = np.random.randint(0, 5, size=(8, 8))
    W_v = np.random.randint(0, 6, size=(8, 8))
    
    # Compute Q, K, V
    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    
    # Reshape to add batch dimension
    Q = Q.reshape(1, *Q.shape)
    K = K.reshape(1, *K.shape)
    V = V.reshape(1, *V.shape)
    mask = mask.reshape(1, *mask.shape)
    
    # Compute masked attention
    result = masked_attention(Q, K, V, mask)
    
    # Print result
    print("Actual result:")
    print(np.round(result[0]).astype(int))
    
    # Expected result
    expected = np.array([
        [547, 490, 399, 495, 485, 439, 645, 393],
        [547, 490, 399, 495, 485, 439, 645, 393],
        [471, 472, 429, 538, 377, 450, 531, 362],
        [471, 472, 429, 538, 377, 450, 531, 362],
        [471, 472, 429, 538, 377, 450, 531, 362],
        [471, 472, 429, 538, 377, 450, 531, 362]
    ])
    
    print("\nExpected result:")
    print(expected)
    
    # Check if they match
    print("\nMatch:", np.allclose(np.round(result[0]).astype(int), expected))

if __name__ == "__main__":
    test_exact_case()