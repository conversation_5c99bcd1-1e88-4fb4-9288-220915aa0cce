# 导入numpy库，用于科学计算
import numpy as np

# 定义层归一化函数
def layer_norm(x, g, b, eps: float = 1e-5):
    # 计算输入的均值
    mean = np.mean(x, axis=-1, keepdims=True)
    # 计算输入的方差
    variance = np.var(x, axis=-1, keepdims=True)
    # 执行层归一化并返回结果
    return g * (x - mean) / np.sqrt(variance + eps) + b

# 导入numpy库，用于��学计算
import numpy as np

# 定义层归一化函数
def layer_norm(x, g, b, eps: float = 1e-5):
    # x 的形状: (n_seq, n_embd)
    # 计算输入的均值
    mean = np.mean(x, axis=-1, keepdims=True) # mean 的形状: (n_seq, 1)
    # 计算输入的方差
    variance = np.var(x, axis=-1, keepdims=True) # variance 的形状: (n_seq, 1)
    # 执行层归一化并返回结果
    # g 的形状: (n_embd,)
    # b 的形状: (n_embd,)
    # 返回的形状: (n_seq, n_embd)
    return g * (x - mean) / np.sqrt(variance + eps) + b

# 定义文本生成函数
def gen_text(prompt: str, n_tokens_to_generate: int = 40):
    # 加载编码器、超参数和模型参数
    encoder, hparams, params = load_encoder_hparams_and_params()
    # 将输入的提示文本编码为token ID
    # input_ids 是一个整数列表
    input_ids = encoder.encode(prompt)
    # 循环生成指定数量的token
    for _ in range(n_tokens_to_generate):
        # 获取上下文窗口内的token ID
        # x 是一个长度为 n_seq 的整数列表
        x = input_ids[-hparams["n_ctx"]:]
        # 计算词嵌入和位置嵌入的总和
        # params["wte"] 的形状: (n_vocab, n_embd)
        # params["wpe"] 的形状: (n_ctx, n_embd)
        # params["wte"][x] 的形状: (n_seq, n_embd)
        # params["wpe"][:len(x)] 的形状: (n_seq, n_embd)
        # h 的形状: (n_seq, n_embd)
        h = params["wte"][x] + params["wpe"][:len(x)]
        # 对嵌入和进行层归一化
        # h 的形状: (n_seq, n_embd)
        h = layer_norm(h, **params["ln_f"])
        # 计算logits，即下一个token的预测分数
        # params["wte"].T 的形状: (n_embd, n_vocab)
        # logits 的形状: (n_seq, n_vocab)
        logits = h @ params["wte"].T
        # 选择分数最高的token作为下一个token
        # logits[-1] 的形状: (n_vocab,)
        # next_token 是一个整数
        next_token = np.argmax(logits[-1])
        # 将新生成的token ID添加到输入序列中
        input_ids.append(int(next_token))
    # 提取生成部分的token ID
    output_ids = input_ids[len(encoder.encode(prompt)):]
    # 解码生成的token ID并返回文本
    return encoder.decode(output_ids)

# 定义加载模型组件的函数
def load_encoder_hparams_and_params(model_size: str = "124M", models_dir: str = "models"):
	# 定义一个虚拟的BPE编码器类
	class DummyBPE:
		# 初始化方法
		def __init__(self):
			# 定义一个简单的词典
			self.encoder_dict = {"hello": 1, "world": 2, "<UNK>": 0}

		# 定义编码方法
		def encode(self, text: str):
			# 将文本分割成token
			tokens = text.strip().split()
			# 将token转换为ID
			return [self.encoder_dict.get(token, self.encoder_dict["<UNK>"]) for token in tokens]

		# 定义解码方法
		def decode(self, token_ids: list):
			# 创建一个反向词典
			reversed_dict = {v: k for k, v in self.encoder_dict.items()}
			# 将ID转换为token并拼接成字符串
			return " ".join([reversed_dict.get(tok_id, "<UNK>") for tok_id in token_ids])

	# 定义超参数
	hparams = {
		# 上下文窗口大小
		"n_ctx": 1024,
		# 注意力头的数量
		"n_head": 12
	}

	# 定义模型参数
	params = {
		# 词嵌入矩阵
		"wte": np.random.rand(3, 10),
		# 位置嵌入矩阵
		"wpe": np.random.rand(1024, 10),
		# Transformer块（此处为空）
		"blocks": [],
		# 最终层归一化的参数
		"ln_f": {
			# 缩放因子
			"g": np.ones(10),
			# 偏移因子
			"b": np.zeros(10),
		}
	}

	# 实例化虚拟编码器
	encoder = DummyBPE()
	# 返回编码器、超参数和参数
	return encoder, hparams, params