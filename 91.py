def calculate_f1_score(y_true, y_pred):
	"""
	Calculate the F1 score based on true and predicted labels.

	Args:
		y_true (list): True labels (ground truth).
		y_pred (list): Predicted labels.

	Returns:
		float: The F1 score rounded to three decimal places.
    Implement a function to calculate the F1 score given predicted and true labels. The F1 score is a widely used metric in machine learning, combining precision and recall into a single measure. round your solution to the 3rd decimal place
    Example:
    Input:

    y_true = [1, 0, 1, 1, 0], y_pred = [1, 0, 0, 1, 1]

    Output:

    0.667

    Reasoning:

    The true positives, false positives, and false negatives are calculated from the given labels. Precision and recall are derived, and the F1 score is computed as their harmonic mean.
	"""
	true_positives = sum(1 for yt, yp in zip(y_true, y_pred) if yt == 1 and yp == 1)
	false_positives = sum(1 for yt, yp in zip(y_true, y_pred) if yt == 0 and yp == 1)
	false_negatives = sum(1 for yt, yp in zip(y_true, y_pred) if yt == 1 and yp == 0)

	if true_positives + false_positives == 0:
		precision = 0.0
	else:
		precision = true_positives / (true_positives + false_positives)

	if true_positives + false_negatives == 0:
		recall = 0.0
	else:
		recall = true_positives / (true_positives + false_negatives)

	if precision + recall == 0:
		f1 = 0.0
	else:
		f1 = 2 * (precision * recall) / (precision + recall)
	return round(f1,3)