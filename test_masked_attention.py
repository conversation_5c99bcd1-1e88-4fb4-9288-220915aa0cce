import numpy as np
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from attention import masked_attention

def test_masked_attention():
    # Create sample data
    batch_size = 2
    seq_len = 4
    d_model = 8
    
    # Create Q, K, V matrices
    Q = np.random.rand(batch_size, seq_len, d_model) * 100
    K = np.random.rand(batch_size, seq_len, d_model) * 100
    V = np.random.rand(batch_size, seq_len, d_model) * 100
    
    # Create causal mask (lower triangular)
    mask = np.triu(np.ones((seq_len, seq_len)) * -1e9, k=1)
    mask = np.expand_dims(mask, axis=0)  # Add batch dimension
    
    # Compute masked attention
    result = masked_attention(Q, K, V, mask)
    
    print("Q shape:", Q.shape)
    print("K shape:", K.shape)
    print("V shape:", V.shape)
    print("Mask shape:", mask.shape)
    print("Result shape:", result.shape)
    print("Result:")
    print(result)

if __name__ == "__main__":
    test_masked_attention()