import numpy as np

def pos_encoding(position: int, d_model: int):
	"""
	The Positional Encoding layer in Transformers plays a critical role by providing necessary positional information to the model. This is particularly important because the Transformer architecture, unlike RNNs or LSTMs, processes input sequences in parallel and lacks inherent mechanisms to account for the sequential order of tokens.

	The mathematical intuition behind the Positional Encoding layer in Transformers is centered on enabling the model to incorporate information about the order of tokens in a sequence.
	Function Parameters

	    position: Total positions or length of the sequence.
	    d_model: Dimensionality of the model's output.

	Generating the Base Matrix

	    angle_rads: Creates a matrix where rows represent sequence positions and columns represent feature dimensions. Values are scaled by dividing each position index by:
	    10000**(2*i/d_model)

	Applying Sine and Cosine Functions

	    For even indices: Apply the sine function to encode positions.
	    PE(pos,2i)=sin(pos/10000**(2i/d_model))

	    For odd indices: Apply the cosine function for a phase-shifted encoding.
	    PE(pos,2i+1)=cos⁡(pos/10000**(2i/d_model))

	Creating the Positional Encoding Tensor

	    The matrix is expanded to match input shape expectations of models like Transformers and cast to float32.

	Output

	Returns a numpy array of shape (1,position,d_model), ready to be added to input embeddings to incorporate positional information.
	"""
	if position <= 0 or d_model <= 0 or d_model % 2 != 0:
		raise ValueError("position and d_model must be positive, and d_model must be even.")

	# Create an array of positions (0, 1, ..., position-1)
	positions = np.arange(position)[:, np.newaxis]

	# Calculate the division term for the angles
	div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))

	# Calculate the arguments for the sine and cosine functions
	angle_rads = positions * div_term

	# Create the positional encoding matrix
	pos_encoding_matrix = np.zeros((position, d_model), dtype=np.float32)

	# Apply sin to even indices (2i)
	pos_encoding_matrix[:, 0::2] = np.sin(angle_rads)

	# Apply cos to odd indices (2i+1)
	pos_encoding_matrix[:, 1::2] = np.cos(angle_rads)

	# Add a batch dimension to match the expected shape (1, position, d_model)
	pos_encoding_tensor = pos_encoding_matrix[np.newaxis, ...]

	return np.round(pos_encoding_tensor.astype(np.float32),4)


if __name__ == "__main__":
    print(pos_encoding(5, 16))
