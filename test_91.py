import pytest
from ninety_one import calculate_f1_score

def test_calculate_f1_score():
    y_true = [1, 0, 1, 1, 0]
    y_pred = [1, 0, 0, 1, 1]
    assert calculate_f1_score(y_true, y_pred) == 0.667

def test_calculate_f1_score_perfect():
    y_true = [1, 0, 1, 1, 0]
    y_pred = [1, 0, 1, 1, 0]
    assert calculate_f1_score(y_true, y_pred) == 1.0

def test_calculate_f1_score_no_positives():
    y_true = [0, 0, 0, 0, 0]
    y_pred = [0, 0, 0, 0, 0]
    assert calculate_f1_score(y_true, y_pred) == 0.0

def test_calculate_f1_score_all_incorrect():
    y_true = [1, 1, 1, 1, 1]
    y_pred = [0, 0, 0, 0, 0]
    assert calculate_f1_score(y_true, y_pred) == 0.0

def test_calculate_f1_score_mixed():
    y_true = [1, 0, 1, 0, 1, 0]
    y_pred = [1, 1, 0, 0, 1, 1]
    # TP = 2 (at index 0, 4)
    # FP = 2 (at index 1, 5)
    # FN = 1 (at index 2)
    # Precision = 2 / (2 + 2) = 0.5
    # Recall = 2 / (2 + 1) = 0.666...
    # F1 = 2 * (0.5 * 0.666...) / (0.5 + 0.666...) = 0.5714...
    assert calculate_f1_score(y_true, y_pred) == 0.571
