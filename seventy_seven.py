
def performance_metrics(actual: list[int], predicted: list[int]) -> tuple:
	"""implement a function performance_metrics(actual, predicted) that computes various performance metrics for a binary classification problem. These metrics include:

        Confusion Matrix
        Accuracy
        F1 Score
        Specificity
        Negative Predictive Value

    The function should take in two lists:

        actual: The actual class labels (1 for positive, 0 for negative).
        predicted: The predicted class labels from the model.

    Output

    The function should return a tuple containing:

        confusion_matrix: A 2x2 matrix.
        accuracy: A float representing the accuracy of the model.
        f1_score: A float representing the F1 score of the model.
        specificity: A float representing the specificity of the model.
        negative_predictive_value: A float representing the negative predictive value.

    Constraints

        All elements in the actual and predicted lists must be either 0 or 1.
        Both lists must have the same length.

    Example:
    Input:

    actual = [1, 0, 1, 0, 1]
    predicted = [1, 0, 0, 1, 1]
    print(performance_metrics(actual, predicted))

    Output:

    ([[2, 1], [1, 1]], 0.6, 0.667, 0.5, 0.5)

    Reasoning:

    The function calculates the confusion matrix, accuracy, F1 score, specificity, and negative predictive value based on the input labels. The resulting values are rounded to three decimal places as required.
    Learn About topic
    """
	if len(actual) != len(predicted):
		raise ValueError("Input lists must have the same length.")

	tp = sum(1 for a, p in zip(actual, predicted) if a == 1 and p == 1)
	tn = sum(1 for a, p in zip(actual, predicted) if a == 0 and p == 0)
	fp = sum(1 for a, p in zip(actual, predicted) if a == 0 and p == 1)
	fn = sum(1 for a, p in zip(actual, predicted) if a == 1 and p == 0)

	confusion_matrix = [[tp, fn], [fp, tn]]

	total = tp + tn + fp + fn
	accuracy = (tp + tn) / total if total > 0 else 0

	precision = tp / (tp + fp) if (tp + fp) > 0 else 0
	recall = tp / (tp + fn) if (tp + fn) > 0 else 0
	f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

	specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
	negativePredictive = tn / (tn + fn) if (tn + fn) > 0 else 0

	return confusion_matrix, round(accuracy, 3), round(f1, 3), round(specificity, 3), round(negativePredictive, 3)
